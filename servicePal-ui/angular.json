{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"servicePal": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/service-pal", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "src/assets"}, {"glob": "favicon.ico", "input": "src", "output": "/"}], "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "./node_modules/ngx-bootstrap/datepicker/bs-datepicker.css", "src/styles.css", "node_modules/ngx-toastr/toastr.css", "node_modules/@fortawesome/fontawesome-free/css/all.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}]}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": false, "outputHashing": "none", "sourceMap": true, "extractLicenses": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "servicePal:build:production"}, "development": {"buildTarget": "servicePal:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "src/assets"}], "styles": ["src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": false}}