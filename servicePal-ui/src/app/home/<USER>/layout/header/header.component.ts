import {Component, Inject, OnInit, <PERSON><PERSON><PERSON><PERSON>, HostListener} from '@angular/core';
import {DOCUMENT} from "@angular/common";
import {CoreApiConstants} from "../../core-constants";
import {UserService} from "../../../admin/service/user.service";
import {PermissionService} from "../../service/permission.service";
import {PermissionsSidebarService} from "../../service/permissions-sidebar.service";
import {AppStateService} from "../../service/app-state.service";
import {Router} from "@angular/router";
import {Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';

@Component({
standalone: false,
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit, OnDestroy {

  elem;
  appVersion: string;
  isAdmin: boolean = false;
  isCheckingAdmin: boolean = false;
  user: any;
  private destroy$ = new Subject<void>();


  constructor(
    @Inject(DOCUMENT) private document: any,
    private userService: UserService,
    private permissionService: PermissionService,
    private permissionsSidebarService: PermissionsSidebarService,
    private appStateService: AppStateService,
    private router: Router
  ) {
    this.appVersion = CoreApiConstants.APP_VERSION;
    this.user = JSON.parse(localStorage.getItem('currentUser'))?.user;
  }

  ngOnInit() {
    this.elem = document.documentElement;
    this.checkIfUserIsAdmin();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Check if the current user has admin role
   */
  checkIfUserIsAdmin() {
    // Prevent multiple simultaneous calls
    if (this.isCheckingAdmin) {
      return;
    }

    this.isCheckingAdmin = true;

    this.userService.isAdmin()
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        (isAdmin: boolean) => {
          this.isAdmin = isAdmin;
          this.isCheckingAdmin = false;
        },
        (error) => {
          console.error('Error checking admin status:', error);
          this.isAdmin = false;
          this.isCheckingAdmin = false;

          // If 401 error, don't retry - let the interceptor handle it
          if (error.status === 401) {
            return;
          }
        }
      );
  }

  /**
   * Navigate to dashboard
   */
  navigateToDashboard() {
    console.log('Navigating to dashboard...');
    this.appStateService.safeNavigate(['/home/<USER>']).then(
      (success) => {
        if (success) {
          console.log('Navigation to dashboard successful');
        } else {
          console.log('Navigation to dashboard failed or skipped');
        }
      }
    );
  }


  openFullscreen() {
    if (this.elem.requestFullscreen) {
      this.elem.requestFullscreen();
    } else if (this.elem.mozRequestFullScreen) {
      this.elem.mozRequestFullScreen();
    } else if (this.elem.webkitRequestFullscreen) {
      this.elem.webkitRequestFullscreen();
    } else if (this.elem.msRequestFullscreen) {
      this.elem.msRequestFullscreen();
    }
  }

  /**
   * Toggle permissions sidebar
   */
  togglePermissionsSidebar() {
    if (!this.permissionsSidebarService.isSidebarVisible()) {
      // Load permissions when opening sidebar
      this.loadUserPermissions();
    }
    this.permissionsSidebarService.toggleSidebar();
  }

  /**
   * Load user permissions and send to sidebar service
   */
  loadUserPermissions() {
    if (!this.user?.username) {
      console.error('No user found in localStorage');
      return;
    }

    this.permissionsSidebarService.setLoading(true);
    this.permissionService.findAvailablePermissions(this.user.username)
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        (permissions: Array<any>) => {
          console.log('Loading permissions for sidebar:', permissions); // Debug log
          this.permissionsSidebarService.setPermissions(permissions);
          this.permissionsSidebarService.setLoading(false);
        },
        (error) => {
          console.error('Error loading permissions:', error);
          this.permissionsSidebarService.setLoading(false);
        }
      );
  }



  protected readonly CoreApiConstants = CoreApiConstants;
}
