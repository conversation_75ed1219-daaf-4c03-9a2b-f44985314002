import {ApiConstants} from '../admin-constants';
import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {User} from '../model/user';

@Injectable({
  providedIn: 'root'
})

export class UserService {

  constructor(private http: HttpClient) {
  }

  public findAll() {
    return this.http.get(ApiConstants.GET_USERS);
  }

  public save(user: User) {
    return this.http.post<any>(ApiConstants.SAVE_USER, user);
  }

  public deleteUser(id) {
    return this.http.delete(ApiConstants.DISABLE_USER, {params: {id: id}});
  }

  public search(search) {
    return this.http.get(ApiConstants.SEARCH_USER, {params: {any: search}});
  }


  checkBike(username: string) {
    return this.http.get(ApiConstants.USER_CHECK, {params: {username: username}});
  }

  findByName(username) {
    return this.http.get(ApiConstants.SEARCH_USER_BY_USERNAME, {params: {username: username}});
  }

  update(user: User) {
    return this.http.post<any>(ApiConstants.UPDATE_USER, user);
  }

  searchByName(username) {
    return this.http.get(ApiConstants.SEARCH_BY_NAME, {params: {username: username}});
  }

  setupCounter(counterId, userId) {
    return this.http.get(ApiConstants.SETUP_COUNTER, {
      params: {
        counterId: counterId,
        userId: userId
      }
    });
  }

  isAdmin() {
    return this.http.get<boolean>(ApiConstants.IS_ADMIN);
  }
}
