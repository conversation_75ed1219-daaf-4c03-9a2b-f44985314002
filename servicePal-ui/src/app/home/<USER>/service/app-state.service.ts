import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AppStateService {

  constructor(private router: Router) { }

  /**
   * Safe navigation method that handles navigation errors
   */
  async safeNavigate(commands: any[]): Promise<boolean> {
    try {
      return await this.router.navigate(commands);
    } catch (error) {
      console.error('Navigation error:', error);
      return false;
    }
  }
}
