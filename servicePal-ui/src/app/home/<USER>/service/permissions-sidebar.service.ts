import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PermissionsSidebarService {

  private sidebarVisibleSubject = new BehaviorSubject<boolean>(false);
  public sidebarVisible$ = this.sidebarVisibleSubject.asObservable();

  private permissionsSubject = new BehaviorSubject<Array<any>>([]);
  public permissions$ = this.permissionsSubject.asObservable();

  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  private readonly SIDEBAR_STATE_KEY = 'permissions-sidebar-state';
  private readonly MODULE_EXPANSION_KEY = 'permissions-sidebar-modules';
  private readonly AUTO_OPEN_KEY = 'permissions-sidebar-auto-open';

  constructor() {
    // Initialize sidebar state from localStorage
    this.initializeSidebarState();
  }

  /**
   * Initialize sidebar state from localStorage
   */
  private initializeSidebarState() {
    try {
      const savedState = localStorage.getItem(this.SIDEBAR_STATE_KEY);
      const autoOpenEnabled = localStorage.getItem(this.AUTO_OPEN_KEY) !== 'false'; // Default to true

      if (savedState === 'true' && autoOpenEnabled) {
        // Auto-open sidebar if it was previously open and auto-open is enabled
        setTimeout(() => {
          this.sidebarVisibleSubject.next(true);
        }, 500); // Small delay to ensure app is fully loaded
      }
    } catch (error) {
      console.warn('Failed to load sidebar state from localStorage:', error);
    }
  }

  /**
   * Save sidebar state to localStorage
   */
  private saveSidebarState(visible: boolean) {
    try {
      localStorage.setItem(this.SIDEBAR_STATE_KEY, visible.toString());
    } catch (error) {
      console.warn('Failed to save sidebar state to localStorage:', error);
    }
  }

  /**
   * Toggle sidebar visibility
   */
  toggleSidebar() {
    const newState = !this.sidebarVisibleSubject.value;
    this.sidebarVisibleSubject.next(newState);
    this.saveSidebarState(newState);
  }

  /**
   * Show sidebar
   */
  showSidebar() {
    this.sidebarVisibleSubject.next(true);
    this.saveSidebarState(true);
  }

  /**
   * Hide sidebar
   */
  hideSidebar() {
    this.sidebarVisibleSubject.next(false);
    this.saveSidebarState(false);
  }

  /**
   * Set permissions data
   */
  setPermissions(permissions: Array<any>) {
    this.permissionsSubject.next(permissions);
  }

  /**
   * Set loading state
   */
  setLoading(loading: boolean) {
    this.loadingSubject.next(loading);
  }

  /**
   * Get current sidebar state
   */
  isSidebarVisible(): boolean {
    return this.sidebarVisibleSubject.value;
  }

  /**
   * Set permissions and show sidebar (convenience method)
   */
  setPermissionsAndShow(permissions: Array<any>) {
    this.setPermissions(permissions);
    this.showSidebar();
  }

  /**
   * Save module expansion state to localStorage
   */
  saveModuleExpansionState(moduleStates: { [moduleName: string]: boolean }) {
    try {
      localStorage.setItem(this.MODULE_EXPANSION_KEY, JSON.stringify(moduleStates));
    } catch (error) {
      console.warn('Failed to save module expansion state to localStorage:', error);
    }
  }

  /**
   * Get module expansion state from localStorage
   */
  getModuleExpansionState(): { [moduleName: string]: boolean } {
    try {
      const savedState = localStorage.getItem(this.MODULE_EXPANSION_KEY);
      return savedState ? JSON.parse(savedState) : {};
    } catch (error) {
      console.warn('Failed to load module expansion state from localStorage:', error);
      return {};
    }
  }

  /**
   * Set auto-open preference
   */
  setAutoOpenEnabled(enabled: boolean) {
    try {
      localStorage.setItem(this.AUTO_OPEN_KEY, enabled.toString());
    } catch (error) {
      console.warn('Failed to save auto-open preference to localStorage:', error);
    }
  }

  /**
   * Get auto-open preference
   */
  isAutoOpenEnabled(): boolean {
    try {
      const autoOpen = localStorage.getItem(this.AUTO_OPEN_KEY);
      return autoOpen !== 'false'; // Default to true if not set
    } catch (error) {
      console.warn('Failed to load auto-open preference from localStorage:', error);
      return true; // Default to true
    }
  }

  /**
   * Clear all saved states (useful for logout)
   */
  clearSavedStates() {
    try {
      localStorage.removeItem(this.SIDEBAR_STATE_KEY);
      localStorage.removeItem(this.MODULE_EXPANSION_KEY);
      localStorage.removeItem(this.AUTO_OPEN_KEY);
    } catch (error) {
      console.warn('Failed to clear sidebar states from localStorage:', error);
    }
  }
}
