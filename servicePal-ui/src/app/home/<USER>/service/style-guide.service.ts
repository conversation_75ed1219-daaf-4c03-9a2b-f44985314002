import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class StyleGuideService {

  constructor() { }

  /**
   * Standard Bootstrap button classes for consistent styling
   */
  getButtonClasses() {
    return {
      primary: 'btn btn-primary',
      secondary: 'btn btn-secondary', 
      success: 'btn btn-success',
      danger: 'btn btn-danger',
      warning: 'btn btn-warning',
      info: 'btn btn-info',
      light: 'btn btn-light',
      dark: 'btn btn-dark',
      outline: {
        primary: 'btn btn-outline-primary',
        secondary: 'btn btn-outline-secondary',
        success: 'btn btn-outline-success',
        danger: 'btn btn-outline-danger',
        warning: 'btn btn-outline-warning',
        info: 'btn btn-outline-info',
        light: 'btn btn-outline-light',
        dark: 'btn btn-outline-dark'
      },
      sizes: {
        small: 'btn-sm',
        large: 'btn-lg'
      }
    };
  }

  /**
   * Standard Bootstrap table classes for professional appearance
   */
  getTableClasses() {
    return {
      basic: 'table',
      striped: 'table table-striped',
      bordered: 'table table-bordered',
      hover: 'table table-hover',
      professional: 'table table-striped table-hover',
      responsive: 'table-responsive',
      sizes: {
        small: 'table-sm'
      },
      variants: {
        dark: 'table-dark',
        light: 'table-light'
      }
    };
  }

  /**
   * Standard Bootstrap card classes
   */
  getCardClasses() {
    return {
      basic: 'card',
      header: 'card-header',
      body: 'card-body',
      footer: 'card-footer',
      title: 'card-title',
      subtitle: 'card-subtitle',
      text: 'card-text',
      variants: {
        primary: 'card border-primary',
        secondary: 'card border-secondary',
        success: 'card border-success',
        danger: 'card border-danger',
        warning: 'card border-warning',
        info: 'card border-info',
        light: 'card border-light',
        dark: 'card border-dark'
      }
    };
  }

  /**
   * Standard Bootstrap form classes
   */
  getFormClasses() {
    return {
      control: 'form-control',
      select: 'form-select',
      check: 'form-check',
      checkInput: 'form-check-input',
      checkLabel: 'form-check-label',
      label: 'form-label',
      text: 'form-text',
      group: 'mb-3',
      inputGroup: 'input-group',
      inputGroupText: 'input-group-text',
      validation: {
        valid: 'is-valid',
        invalid: 'is-invalid',
        feedback: 'invalid-feedback',
        validFeedback: 'valid-feedback'
      },
      sizes: {
        small: 'form-control-sm',
        large: 'form-control-lg'
      }
    };
  }

  /**
   * Standard Bootstrap alert classes
   */
  getAlertClasses() {
    return {
      primary: 'alert alert-primary',
      secondary: 'alert alert-secondary',
      success: 'alert alert-success',
      danger: 'alert alert-danger',
      warning: 'alert alert-warning',
      info: 'alert alert-info',
      light: 'alert alert-light',
      dark: 'alert alert-dark',
      dismissible: 'alert-dismissible'
    };
  }

  /**
   * Standard Bootstrap modal classes
   */
  getModalClasses() {
    return {
      modal: 'modal',
      dialog: 'modal-dialog',
      content: 'modal-content',
      header: 'modal-header',
      title: 'modal-title',
      body: 'modal-body',
      footer: 'modal-footer',
      sizes: {
        small: 'modal-sm',
        large: 'modal-lg',
        extraLarge: 'modal-xl'
      },
      centered: 'modal-dialog-centered',
      scrollable: 'modal-dialog-scrollable'
    };
  }

  /**
   * Standard Bootstrap spacing utilities
   */
  getSpacingClasses() {
    return {
      margin: {
        all: 'm-',
        top: 'mt-',
        bottom: 'mb-',
        left: 'ms-',
        right: 'me-',
        x: 'mx-',
        y: 'my-'
      },
      padding: {
        all: 'p-',
        top: 'pt-',
        bottom: 'pb-',
        left: 'ps-',
        right: 'pe-',
        x: 'px-',
        y: 'py-'
      },
      sizes: ['0', '1', '2', '3', '4', '5', 'auto']
    };
  }

  /**
   * Standard Bootstrap text utilities
   */
  getTextClasses() {
    return {
      colors: {
        primary: 'text-primary',
        secondary: 'text-secondary',
        success: 'text-success',
        danger: 'text-danger',
        warning: 'text-warning',
        info: 'text-info',
        light: 'text-light',
        dark: 'text-dark',
        muted: 'text-muted',
        white: 'text-white'
      },
      alignment: {
        start: 'text-start',
        center: 'text-center',
        end: 'text-end'
      },
      weight: {
        bold: 'fw-bold',
        bolder: 'fw-bolder',
        semibold: 'fw-semibold',
        normal: 'fw-normal',
        light: 'fw-light',
        lighter: 'fw-lighter'
      },
      decoration: {
        underline: 'text-decoration-underline',
        lineThrough: 'text-decoration-line-through',
        none: 'text-decoration-none'
      }
    };
  }

  /**
   * Standard Bootstrap background utilities
   */
  getBackgroundClasses() {
    return {
      primary: 'bg-primary',
      secondary: 'bg-secondary',
      success: 'bg-success',
      danger: 'bg-danger',
      warning: 'bg-warning',
      info: 'bg-info',
      light: 'bg-light',
      dark: 'bg-dark',
      white: 'bg-white',
      transparent: 'bg-transparent'
    };
  }

  /**
   * Standard Bootstrap flexbox utilities
   */
  getFlexClasses() {
    return {
      display: 'd-flex',
      direction: {
        row: 'flex-row',
        column: 'flex-column',
        rowReverse: 'flex-row-reverse',
        columnReverse: 'flex-column-reverse'
      },
      justify: {
        start: 'justify-content-start',
        end: 'justify-content-end',
        center: 'justify-content-center',
        between: 'justify-content-between',
        around: 'justify-content-around',
        evenly: 'justify-content-evenly'
      },
      align: {
        start: 'align-items-start',
        end: 'align-items-end',
        center: 'align-items-center',
        baseline: 'align-items-baseline',
        stretch: 'align-items-stretch'
      },
      wrap: {
        wrap: 'flex-wrap',
        nowrap: 'flex-nowrap',
        reverse: 'flex-wrap-reverse'
      }
    };
  }

  /**
   * Get complete professional table class string
   */
  getProfessionalTableClass(): string {
    return 'table table-striped table-hover table-responsive';
  }

  /**
   * Get complete professional form group class string
   */
  getProfessionalFormGroupClass(): string {
    return 'mb-3';
  }

  /**
   * Get complete professional card class string
   */
  getProfessionalCardClass(): string {
    return 'card shadow-sm';
  }

  /**
   * Get complete professional button class string for specific actions
   */
  getProfessionalButtonClass(action: 'save' | 'edit' | 'delete' | 'cancel' | 'add' | 'view'): string {
    const buttonMap = {
      save: 'btn btn-success',
      edit: 'btn btn-warning',
      delete: 'btn btn-danger',
      cancel: 'btn btn-secondary',
      add: 'btn btn-primary',
      view: 'btn btn-info'
    };
    return buttonMap[action] || 'btn btn-primary';
  }
}
