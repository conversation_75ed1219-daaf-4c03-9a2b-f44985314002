import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class IconService {

  constructor() { }

  /**
   * Get standardized icon for module based on module name
   * Uses consistent Bootstrap color scheme
   */
  getModuleIcon(moduleName: string): string {
    const moduleIcons = {
      'Inventory': 'fas fa-boxes text-primary',
      'Trade': 'fas fa-handshake text-success',
      'Report': 'fas fa-chart-line text-info',
      'Admin': 'fas fa-user-cog text-warning',
      'HR': 'fas fa-users text-secondary',
      'Dashboard': 'fas fa-tachometer-alt text-danger',
      'Core': 'fas fa-cog text-dark',
      'Restaurant': 'fas fa-utensils text-purple',
      'Booking': 'fas fa-calendar-check text-info'
    };

    return moduleIcons[moduleName] || 'fas fa-folder text-muted';
  }

  /**
   * Get standardized permission icons based on permission name/type
   * Provides consistent iconography across the application
   */
  getPermissionIcon(permissionName: string): string {
    const permissionIcons = {
      // Inventory related
      'Create Item': 'fas fa-plus-circle text-success',
      'View All Items': 'fas fa-list text-primary',
      'Edit Item': 'fas fa-edit text-warning',
      'Delete Item': 'fas fa-trash text-danger',
      'Item Categories': 'fas fa-tags text-info',
      'Brands': 'fas fa-trademark text-secondary',
      'Stock Management': 'fas fa-warehouse text-primary',

      // Trade/Sales related
      'Create Invoice': 'fas fa-file-invoice text-success',
      'View Invoices': 'fas fa-file-invoice-dollar text-primary',
      'Customer Management': 'fas fa-users text-secondary',
      'Supplier Management': 'fas fa-truck text-warning',

      // Admin related
      'User Management': 'fas fa-user-cog text-warning',
      'Role Management': 'fas fa-user-shield text-danger',
      'System Settings': 'fas fa-cogs text-dark',
      'Backup': 'fas fa-database text-info',

      // HR related
      'Employee Management': 'fas fa-id-card text-primary',
      'Attendance': 'fas fa-clock text-success',
      'Payroll': 'fas fa-money-bill-wave text-warning',
      'Leave Management': 'fas fa-calendar-times text-info',

      // Reports (consolidated - no duplicates)
      'Sales Report': 'fas fa-chart-line text-success',
      'Income Report': 'fas fa-chart-line text-success',
      'Inventory Report': 'fas fa-chart-bar text-primary',
      'Stock Report': 'fas fa-chart-bar text-primary',
      'Financial Report': 'fas fa-chart-pie text-warning',
      'Profit Report': 'fas fa-chart-pie text-warning',
      'Custom Report': 'fas fa-file-alt text-info',

      // Restaurant specific
      'Menu Management': 'fas fa-utensils text-primary',
      'Table Management': 'fas fa-chair text-secondary',
      'Kitchen Orders': 'fas fa-fire text-danger',
      'Restaurant POS': 'fas fa-cash-register text-success',

      // Booking specific
      'Room Booking': 'fas fa-bed text-primary',
      'Event Booking': 'fas fa-calendar-plus text-success',
      'Booking Report': 'fas fa-calendar-alt text-info',

      // Core/General
      'Dashboard': 'fas fa-tachometer-alt text-primary',
      'Settings': 'fas fa-cog text-secondary',
      'Profile': 'fas fa-user text-info',
      'Notifications': 'fas fa-bell text-warning'
    };

    // Try exact match first
    if (permissionIcons[permissionName]) {
      return permissionIcons[permissionName];
    }

    // Fallback to pattern matching for dynamic permission names
    const lowerName = permissionName.toLowerCase();

    if (lowerName.includes('create') || lowerName.includes('add') || lowerName.includes('new')) {
      return 'fas fa-plus-circle text-success';
    }
    if (lowerName.includes('view') || lowerName.includes('list') || lowerName.includes('all')) {
      return 'fas fa-list text-primary';
    }
    if (lowerName.includes('edit') || lowerName.includes('update') || lowerName.includes('modify')) {
      return 'fas fa-edit text-warning';
    }
    if (lowerName.includes('delete') || lowerName.includes('remove')) {
      return 'fas fa-trash text-danger';
    }
    if (lowerName.includes('report') || lowerName.includes('analytics')) {
      return 'fas fa-chart-bar text-info';
    }
    if (lowerName.includes('setting') || lowerName.includes('config')) {
      return 'fas fa-cog text-secondary';
    }
    if (lowerName.includes('user') || lowerName.includes('employee')) {
      return 'fas fa-user text-primary';
    }
    if (lowerName.includes('invoice') || lowerName.includes('bill')) {
      return 'fas fa-file-invoice text-success';
    }
    if (lowerName.includes('stock') || lowerName.includes('inventory')) {
      return 'fas fa-warehouse text-primary';
    }

    // Default icon for unknown permissions
    return 'fas fa-circle text-muted';
  }

  /**
   * Get action icons for common CRUD operations
   */
  getActionIcon(action: string): string {
    const actionIcons = {
      'create': 'fas fa-plus text-success',
      'add': 'fas fa-plus text-success',
      'edit': 'fas fa-edit text-warning',
      'update': 'fas fa-sync text-warning',
      'delete': 'fas fa-trash text-danger',
      'remove': 'fas fa-minus text-danger',
      'view': 'fas fa-eye text-primary',
      'search': 'fas fa-search text-info',
      'filter': 'fas fa-filter text-info',
      'print': 'fas fa-print text-secondary',
      'download': 'fas fa-download text-success',
      'upload': 'fas fa-upload text-primary',
      'save': 'fas fa-save text-success',
      'cancel': 'fas fa-times text-secondary',
      'reset': 'fas fa-undo text-warning',
      'refresh': 'fas fa-sync-alt text-info'
    };

    return actionIcons[action.toLowerCase()] || 'fas fa-circle text-muted';
  }

  /**
   * Get status icons for different states
   */
  getStatusIcon(status: string): string {
    const statusIcons = {
      'active': 'fas fa-check-circle text-success',
      'inactive': 'fas fa-times-circle text-danger',
      'pending': 'fas fa-clock text-warning',
      'approved': 'fas fa-thumbs-up text-success',
      'rejected': 'fas fa-thumbs-down text-danger',
      'draft': 'fas fa-file-alt text-secondary',
      'published': 'fas fa-globe text-primary',
      'archived': 'fas fa-archive text-muted'
    };

    return statusIcons[status.toLowerCase()] || 'fas fa-question-circle text-muted';
  }
}
