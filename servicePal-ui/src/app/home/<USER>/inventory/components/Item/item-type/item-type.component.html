<div class="card">
  <div class="card-header">
    <strong>Manage Item Type</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-6">
        <div class="form-group">
          <div class="input-group mb-2">
            <input [(ngModel)]="keyItemType"
                   [typeahead]="itemTypesSearched"
                   (typeaheadLoading)="loadItemCategories()"
                   (typeaheadOnSelect)="setSelectedItemCategory($event)"
                   [typeaheadOptionsLimit]="7"

                   typeaheadOptionField="name"
                   placeholder="Search Item Types"
                   autocomplete="off"
                   size="16"
                   class="form-control" name="item">
          </div>
        </div>

        <table class="table table-hover">
          <thead>
          <tr>
            <th>Item Name</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let itemCat of itemTypes"
              (click)="onSelect(itemCat)" [class.active]="itemCat === selectedItem">
            <td>{{itemCat.name}}</td>
          </tr>
          </tbody>
        </table>

        <div class="row g-3">
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center mt-2"
              [totalItems]="collectionSize"
              [(ngModel)]="page"
              (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>


      </div>
      <div class="col-md-6">


        <form (ngSubmit)="save()" #ManageItemCategoryForm="ngForm">
          <label>Item Name</label>
          <div class="form-group">
            <input type="text" required #itemCategoryName="ngModel"
                   class="form-control" id="itemCategoryName" [(ngModel)]="itemType.name"
                   name="categoryName"
                   placeholder=" Item Category Name">
            <div *ngIf="itemCategoryName.errors && (itemCategoryName.invalid || itemCategoryName.touched)">
              <small class="text-danger" [class.d-none]="itemCategoryName.valid || itemCategoryName.untouched">*item
                type Name is required
              </small>
            </div>
          </div>

          <label>Description</label>
          <div class="form-group">
            <textarea required #Description="ngModel"
                      class="form-control" id="Description" [(ngModel)]="itemType.description" name="description"
                      placeholder="Description ">

              </textarea>
            <div *ngIf="Description.errors && (Description.invalid || Description.touched)">
              <small class="text-danger" [class.d-none]="Description.valid || Description.untouched">*Description is
                required
              </small>
            </div>
          </div>

          <div class="form-check checkbox me-2">
            <input class="form-check-input" id="active" name="active" type="checkbox"
                   [(ngModel)]="itemType.active">
            <label class="form-check-label" for="active">Active</label>
          </div>

          <div class="row float-end">
            <div class="w-100">
              <button class="btn btn-success me-3"
                      [disabled]="selectedItem != null && !ManageItemCategoryForm.form.valid">
                Save
              </button>
              <button type="button" class="btn btn-primary me-3"
                      [disabled]="selectedItem === null && !ManageItemCategoryForm.form.valid"
                      (click)="openModal(templateUpdate)">Update
              </button>
              <button type="button" (click)="clear()" class="btn btn-warning me-3">Clear</button>
            </div>
          </div>
        </form>
      </div>
    </div>

  </div>
</div>

<ng-template #templateUpdate>
  <div class="modal-body text-center ">
    <p>Do you really want to update selected?</p>
    <button type="button" class="btn btn-default" (click)="confirmUpdate()">Yes</button>
    <button type="button" class="btn btn-primary" (click)="decline()">No</button>
  </div>
</ng-template>


