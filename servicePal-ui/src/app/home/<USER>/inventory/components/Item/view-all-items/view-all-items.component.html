<div class="card shadow-sm">
  <div class="card-header bg-primary text-white">
    <h5 class="mb-0">
      <i class="fas fa-list me-2"></i>
      View All Items
    </h5>
  </div>
  <div class="card-body">
    <!-- Search Filters -->
    <div class="row g-3 mb-4">
      <div class="col-md-3">
        <label class="form-label fw-semibold">Search by Item Name</label>
        <div class="input-group">
          <span class="input-group-text">
            <i class="fas fa-search text-muted"></i>
          </span>
          <input [(ngModel)]="keyItemSearch"
                 [typeahead]="itemSearched"
                 (typeaheadLoading)="loadItems()"
                 (typeaheadOnSelect)="setSelectedItem($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="itemName"
                 placeholder="Search By Items"
                 autocomplete="off"
                 class="form-control" name="searchItem">
        </div>
      </div>
      <div class="col-md-3">
        <label class="form-label fw-semibold">Search by Barcode</label>
        <div class="input-group">
          <span class="input-group-text">
            <i class="fas fa-barcode text-muted"></i>
          </span>
          <input [(ngModel)]="barcode"
                 [typeahead]="itemSearched"
                 (typeaheadLoading)="loadItemBarcode()"
                 (typeaheadOnSelect)="setSelectedItemByBarcode($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="barcode"
                 autocomplete="off"
                 placeholder="Search By Barcode"
                 class="form-control" name="category">
        </div>
      </div>
      <div class="col-md-3">
        <label class="form-label fw-semibold">Search by Category</label>
        <div class="input-group">
          <span class="input-group-text">
            <i class="fas fa-tags text-muted"></i>
          </span>
          <input [(ngModel)]="keyItemCategory"
                 [typeahead]="itemCategories"
                 (typeaheadLoading)="loadItemCategories()"
                 (typeaheadOnSelect)="setSelectedItemCategory($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="categoryName"
                 placeholder="Search By Category"
                 autocomplete="off"
                 #category="ngModel"
                 class="form-control" name="category">
        </div>
      </div>
      <div class="col-md-3">
        <label class="form-label fw-semibold">Search by Brand</label>
        <div class="input-group">
          <span class="input-group-text">
            <i class="fas fa-trademark text-muted"></i>
          </span>
          <input [(ngModel)]="keyBrand"
                 [typeahead]="brands"
                 (typeaheadLoading)="loadBrands()"
                 (typeaheadOnSelect)="setSelectedBrand($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadOptionField="name"
                 placeholder="Search By Brand"
                 autocomplete="off"
                 #brand="ngModel"
                 class="form-control" name="brand">
        </div>
      </div>
    </div>

    <!-- Items Table -->
    <div class="table-responsive" (load)="findAllItems()">
      <table class="table table-striped table-hover">
        <thead class="table-dark">
        <tr class="text-center">
          <th scope="col">
            <i class="fas fa-barcode me-1"></i>
            BarCode
          </th>
          <th scope="col">
            <i class="fas fa-box me-1"></i>
            Item Name
          </th>
          <th scope="col">
            <i class="fas fa-tags me-1"></i>
            Category
          </th>
          <th scope="col">
            <i class="fas fa-trademark me-1"></i>
            Brand
          </th>
          <th scope="col">
            <i class="fas fa-dollar-sign me-1"></i>
            Price
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let item of items,let i = index"
            (click)="selectRecord(item,i)"
            [class.active]="i === selectedRow"
            class="text-center cursor-pointer">
          <td class="fw-semibold">{{item.barcode}}</td>
          <td>{{item.itemName}}</td>
          <td>
            <span class="badge bg-info">
              {{item.itemCategory ? item.itemCategory.categoryName : 'N/A'}}
            </span>
          </td>
          <td>
            <span class="badge bg-secondary">
              {{item.brand ? item.brand.name : 'N/A'}}
            </span>
          </td>
          <td class="fw-bold text-success">
            <i class="fas fa-rupee-sign me-1"></i>
            {{item.sellingPrice | currency:'LKR':'symbol':'1.2-2'}}
          </td>
        </tr>
        </tbody>
      </table>

      <!-- Empty State -->
      <div *ngIf="items.length === 0" class="text-center py-5">
        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No items found</h5>
        <p class="text-muted">Try adjusting your search criteria</p>
      </div>
    </div>

    <!-- Pagination -->
    <div class="row g-3 mt-3" *ngIf="items.length > 0">
      <div class="col-md-12 d-flex justify-content-center">
        <pagination
          class="pagination-sm"
          [totalItems]="collectionSize"
          [(ngModel)]="page"
          [maxSize]="15"
          [boundaryLinks]="true"
          (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-end gap-2 mt-4">
      <button type="button" class="btn btn-outline-primary" (click)="loadItems()">
        <i class="fas fa-sync-alt me-1"></i>
        Reset Table
      </button>
      <button type="button" class="btn btn-warning" (click)="edit()" [disabled]="selectedRow === null">
        <i class="fas fa-edit me-1"></i>
        Edit
      </button>
      <button type="button" class="btn btn-success" (click)="openModalBarcode()" [disabled]="selectedRow === null">
        <i class="fas fa-barcode me-1"></i>
        Generate Barcode
      </button>
    </div>
  </div>
</div>




