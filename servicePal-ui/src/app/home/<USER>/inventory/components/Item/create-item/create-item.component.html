<div class="card shadow-sm">
  <div class="card-header bg-success text-white">
    <h5 class="mb-0">
      <i class="fas fa-plus-circle me-2"></i>
      Create New Item
    </h5>
  </div>
  <div class="card-body">
    <form (ngSubmit)="saveItem(createItemForm)" #createItemForm="ngForm">
      <div class="row g-3">
        <!-- Item Type -->
        <div class="col-md-3">
          <label class="form-label fw-semibold">
            <i class="fas fa-tags me-1 text-primary"></i>
            Item Type <span class="text-danger">*</span>
          </label>
          <select class="form-select" (change)="setSelectedItemType($event)" name="itemTypeSelect"
                  [(ngModel)]="itemTypeId" required #itemTypeSelect="ngModel"
                  [class.is-invalid]="itemTypeSelect.invalid && itemTypeSelect.touched">
            <option value="">-Select Item Type-</option>
            <option *ngFor="let ity of itemTypes" [value]="ity.id">
              {{ ity.name }}
            </option>
          </select>
          <div class="invalid-feedback">
            Please select an item type.
          </div>
        </div>

        <!-- Barcode -->
        <div class="col-md-3">
          <label class="form-label fw-semibold">
            <i class="fas fa-barcode me-1 text-primary"></i>
            Barcode <span class="text-danger">*</span>
          </label>
          <input type="text" required #barcode="ngModel"
                 [class.is-invalid]="barcode.invalid && barcode.touched || itemAvailable"
                 class="form-control" id="barcode" [(ngModel)]="item.barcode" name="barcode"
                 placeholder="Enter barcode" (ngModelChange)="checkItemAvailable()" autocomplete="off">
          <div class="invalid-feedback" *ngIf="barcode.invalid && barcode.touched">
            Barcode is required.
          </div>
          <div class="text-danger small" *ngIf="itemAvailable">
            <i class="fas fa-exclamation-triangle me-1"></i>
            This item is already available
          </div>
        </div>

        <!-- Item Name -->
        <div class="col-md-3">
          <label class="form-label fw-semibold">
            <i class="fas fa-box me-1 text-primary"></i>
            Item Name <span class="text-danger">*</span>
          </label>
          <input type="text" required #itemName="ngModel" [class.is-invalid]="itemName.invalid && itemName.touched"
                 class="form-control" id="itemName" [(ngModel)]="item.itemName" name="itemName"
                 placeholder="Enter item name" autocomplete="off">
          <div class="invalid-feedback">
            Item name is required.
          </div>
        </div>

        <!-- Item Category -->
        <div class="col-md-3">
          <label class="form-label fw-semibold">
            <i class="fas fa-sitemap me-1 text-primary"></i>
            Item Category
          </label>
          <div class="input-group">
            <input [(ngModel)]="keyItemCategory"
                   [typeahead]="categories"
                   (typeaheadLoading)="loadItemCategories()"
                   (typeaheadOnSelect)="setSelectedItemCategory($event)"
                   typeaheadOptionField="categoryName"
                   placeholder="Search Item Categories"
                   autocomplete="off"
                   #category="ngModel"
                   [class.is-invalid]="selectedItemCategory.id && category.touched"
                   class="form-control" name="category">
            <button class="btn btn-outline-primary" (click)="openModalLarge(templateItemCategory)"
                    type="button" title="Add New Category">
              <i class="fas fa-plus"></i>
            </button>
          </div>
        </div>
        <div class="mb-3 col-md-3">
          <label>Sub Category</label>
          <div class="input-group">
            <input [(ngModel)]="keySubCategory"
                   [typeahead]="subCategories"
                   (typeaheadLoading)="loadSubCategories()"
                   (typeaheadOnSelect)="setSelectedSubCategory($event)"
                   typeaheadOptionField="subCategoryName"
                   placeholder="Search Sub Categories"
                   autocomplete="off"
                   size="16"
                   #subCategory="ngModel" name="subCategory"
                   [class.is-invalid]="selectedSubCategory.id && subCategory.touched"
                   class="form-control">
            <button class="btn btn-primary fa fa-plus" (click)="openModalExtraLarge(templateSubCategory)"
                    type="button"></button>
          </div>
        </div>
        <div class="mb-3 col-md-6">
          <label>Description</label>
          <input type="text" #description="ngModel" [class.is-invalid]="description.invalid && description.touched"
                 class="form-control" id="Description" [(ngModel)]="item.description" name="Description"
                 placeholder="Description">
        </div>

        <div class="mb-3 col-md-3">
          <label>UOM</label>
          <div class="input-group">
            <input [(ngModel)]="keyUOM"
                   [typeahead]="unitOfMeasure"
                   (typeaheadLoading)="loadUOM()"
                   (typeaheadOnSelect)="setSelectedUOM($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="name"
                   placeholder="Search UOM"
                   autocomplete="off"
                   size="16"
                   #uom="ngModel" [class.is-invalid]="selectedUom.id == undefined && uom.touched"
                   class="form-control" name="uom">
            <button class="btn btn-primary fa fa-plus" (click)="openModalLarge(templateUom)"
                    type="button"></button>
          </div>
        </div>

        <div class="mb-3 col-md-3">
          <label>Common Code</label>
          <input type="text" #commonCode="ngModel"
                 class="form-control" id="commonCode" [(ngModel)]="item.commonCode" name="commonCode"
                 placeholder="Common Code">
        </div>
        <div class="mb-3 col-md-3">
          <label>Brand</label>
          <div class="input-group">
            <input [(ngModel)]="keyBrand"
                   [typeahead]="brands"
                   (typeaheadLoading)="loadBrands()"
                   (typeaheadOnSelect)="setSelectedBrand($event)"
                   [typeaheadOptionsLimit]="10"

                   typeaheadOptionField="name"
                   placeholder="Search Brand"
                   autocomplete="off"
                   id="appendedInputButtons" size="16"
                   class="form-control m-" name="brand">
            <button class="btn btn-primary fa fa-plus" (click)="openModalLarge(templateBrand)"
                    type="button"></button>
          </div>
        </div>
        <div class="mb-3 col-md-2" *ngIf="!isService">
          <label>Item Cost</label>
          <input type="number" #itemCost="ngModel" class="form-control" id="itemCost" [(ngModel)]="item.itemCost"
                 name="itemCost" (ngModelChange)="setSellingPrice()"
                 placeholder="Selling Price" [class.is-invalid]="itemCost.invalid && itemCost.touched">
        </div>
        <div class="mb-3 col-md-2" *ngIf="!isService">
          <label>Supplier Discount</label>
          <input type="number" #itemCostPercentage="ngModel"
                 class="form-control" id="itemCostPercentage" [(ngModel)]="discount" (ngModelChange)="setSellingPrice()"
                 name="itemCostPercentage">
        </div>
        <div class="mb-3 col-md-4" *ngIf="isService">
          <label>Employee Commission</label>
          <input type="number" class="form-control" id="employeeDiscount" [(ngModel)]="item.serviceCommission"
                 name="employeeCommission">
        </div>
        <div class="mb-3 col-md-2"><label>Selling Price</label>
          <input type="number" #sellingPrice="ngModel" required
                 class="form-control" id="sellingPrice" [(ngModel)]="item.sellingPrice" name="sellingPrice"
                 placeholder="Selling Price" [class.is-invalid]="sellingPrice.invalid && sellingPrice.touched">
        </div>
        <div class="mb-3 col-md-3">
          <label>Used For Model</label>
          <input type="text" #ModelNo="ngModel"
                 class="form-control" id="ModelNo" [(ngModel)]="item.model" name="ModelNo"
                 placeholder="Used For Model">
        </div>
        <div class="mb-3 col-md-3">
          <label>Rack or Location</label>
          <div class="input-group">
            <select (change)="onChangeRack(rackId)"
                    class="form-control" id="location" [(ngModel)]="rackId" name="location"
                    placeholder="Rack or Location">
              <option>-Select-</option>
              <option *ngFor="let rack of racks" [value]="rack.id">{{ rack.rackNo }}</option>
            </select>
            <button class="btn btn-primary fa fa-plus" (click)="newRack()"
                    type="button"></button>
          </div>
        </div>
        <div class="mb-3 col-md-3">
          <label>Dead Stock Level</label>
          <input type="number" #dStockeLevel="ngModel"
                 class="form-control" id="dSokeLevel" [(ngModel)]="item.deadStockLevel" name="dStockeLevel"
                 placeholder="Dead Stock Level">
        </div>
        <div class="mb-3 col-md-3">
          <label>Initial Quantity</label>
          <input type="number" #initQuantity="ngModel"
                 class="form-control" id="initQuantity" [(ngModel)]="item.quantity" name="initQuantity"
                 placeholder="Initial Quantity">
        </div>
        <div class="mb-3 col-md-6">
          <div class="form-check checkbox me-2">
            <input class="form-check-input" id="check2" name="check2" type="checkbox" value=""
                   [(ngModel)]="item.manageStock">
            <label class="form-check-label" for="check2">Manage Stock</label>
          </div>
          <div class="form-check checkbox me-2">
            <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                   [(ngModel)]="item.active">
            <label class="form-check-label" for="check3">Active</label>
          </div>
          <div class="form-check checkbox me-2">
            <input class="form-check-input" id="check4" name="check4" type="checkbox" value=""
                   [(ngModel)]="item.autoInactive">
            <label class="form-check-label" for="check3">Auto Inactive</label>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12">
          <div class="d-flex justify-content-end gap-2 mt-4 pt-3 border-top">
            <button type="button" class="btn btn-outline-secondary" (click)="clearForm()">
              <i class="fas fa-undo me-1"></i>
              Clear Form
            </button>
            <button type="submit" class="btn btn-success"
                    [disabled]="(!createItemForm.form.valid || itemAvailable)">
              <i class="fas fa-save me-1"></i>
              Save Item
            </button>
            <button type="button" class="btn btn-primary"
                    [disabled]="(!createItemForm.form.valid || itemAvailable)"
                    (click)="saveAndBarcode(createItemForm)">
              <i class="fas fa-barcode me-1"></i>
              Save & Generate Barcode
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- Modal Templates with proper Bootstrap 5 styling -->
<ng-template #templateBrand>
  <div class="modal-header bg-primary text-white">
    <h5 class="modal-title">
      <i class="fas fa-trademark me-2"></i>
      Add New Brand
    </h5>
    <button type="button" class="btn-close btn-close-white" aria-label="Close" (click)="modalRef.hide()"></button>
  </div>
  <div class="modal-body p-4">
    <app-brand></app-brand>
  </div>
</ng-template>

<ng-template #templateItemCategory>
  <div class="modal-header bg-info text-white">
    <h5 class="modal-title">
      <i class="fas fa-sitemap me-2"></i>
      Add Item Category
    </h5>
    <button type="button" class="btn-close btn-close-white" aria-label="Close" (click)="modalRef.hide()"></button>
  </div>
  <div class="modal-body p-4">
    <app-item-category></app-item-category>
  </div>
</ng-template>

<ng-template #templateSubCategory>
  <div class="modal-header bg-secondary text-white">
    <h5 class="modal-title">
      <i class="fas fa-layer-group me-2"></i>
      Add Sub Category
    </h5>
    <button type="button" class="btn-close btn-close-white" aria-label="Close" (click)="modalRef.hide()"></button>
  </div>
  <div class="modal-body p-4">
    <app-sub-category></app-sub-category>
  </div>
</ng-template>

<ng-template #templateItemType>
  <div class="modal-header bg-warning text-dark">
    <h5 class="modal-title">
      <i class="fas fa-tags me-2"></i>
      Add Item Type
    </h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="modalRef.hide()"></button>
  </div>
  <div class="modal-body p-4">
    <app-item-type></app-item-type>
  </div>
</ng-template>

<ng-template #templateUom>
  <div class="modal-header bg-success text-white">
    <h5 class="modal-title">
      <i class="fas fa-balance-scale me-2"></i>
      Add Unit Of Measure
    </h5>
    <button type="button" class="btn-close btn-close-white" aria-label="Close" (click)="modalRef.hide()"></button>
  </div>
  <div class="modal-body p-4">
    <app-uom></app-uom>
  </div>
</ng-template>

