<div class="card border-0">
  <div class="card-header bg-transparent border-0 p-0 mb-3" *ngIf="!isModal">
    <h5 class="mb-0">
      <i class="fas fa-trademark me-2 text-primary"></i>
      Manage Brands
    </h5>
  </div>
  <div class="card-body p-0">
    <div class="row g-4">
      <!-- Left Section: Search and Table -->
      <div class="col-md-6">
        <div class="row g-3 mb-3">
          <div class="col-md-6">
            <label class="form-label fw-semibold small">Search by Name</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-search text-muted"></i>
              </span>
              <input [(ngModel)]="keyBrand"
                     [typeahead]="brands"
                     (typeaheadLoading)="loadBrands()"
                     (typeaheadOnSelect)="setSelectedBrand($event)"
                     placeholder="Search By Name"
                     autocomplete="off"
                     class="form-control">
            </div>
          </div>
          <div class="col-md-6">
            <label class="form-label fw-semibold small">Search by Code</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-code text-muted"></i>
              </span>
              <input [(ngModel)]="keyBrandCode"
                     [typeahead]="brands"
                     (typeaheadLoading)="loadBrandsByCode()"
                     (typeaheadOnSelect)="setSelectedBrand($event)"
                     placeholder="Search By Code"
                     autocomplete="off"
                     class="form-control">
            </div>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-striped table-hover mt-3">
            <thead class="table-dark">
            <tr>
              <th scope="col">
                <i class="fas fa-trademark me-1"></i>
                Brand Name
              </th>
              <th scope="col">
                <i class="fas fa-code me-1"></i>
                Code
              </th>
              <th scope="col">
                <i class="fas fa-truck me-1"></i>
                Supplier
              </th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let brand of brands, let i=index"
                (click)="brandDetail(brand, i)"
                [class.active]="i === selectedRow"
                class="cursor-pointer">
              <td class="fw-semibold">{{brand.name}}</td>
              <td>
                <span class="badge bg-secondary">{{brand.code}}</span>
              </td>
              <td>{{brand.supplier ? brand.supplier.name : 'N/A'}}</td>
            </tr>
            </tbody>
          </table>
        </div>

        <div class="d-flex justify-content-center mt-3">
          <pagination class="pagination-sm"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      [boundaryLinks]="true"
                      [maxSize]="10"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>

      <!-- Right Section: Form -->
      <div class="col-md-6">
        <form #manageBrandForm="ngForm" (ngSubmit)="saveBrand(); manageBrandForm.reset()">
          <div class="mb-3">
            <label for="bName" class="form-label fw-semibold">
              <i class="fas fa-trademark me-1 text-primary"></i>
              Brand Name <span class="text-danger">*</span>
            </label>
            <input type="text" required #bName="ngModel"
                   [class.is-invalid]="bName.invalid && bName.touched"
                   class="form-control" id="bName"
                   [(ngModel)]="brand.name" name="bName"
                   placeholder="Enter brand name">
            <div class="invalid-feedback" *ngIf="bName.errors && (bName.invalid || bName.touched)">
              Brand Name is required
            </div>
          </div>

          <div class="mb-3">
            <label for="code" class="form-label fw-semibold">
              <i class="fas fa-code me-1 text-primary"></i>
              Code <span class="text-danger">*</span>
            </label>
            <input type="text" required #brandCode="ngModel"
                   class="form-control" id="code"
                   [(ngModel)]="brand.code" maxlength="2"
                   name="itemCost" (ngModelChange)="checkDuplicate()"
                   placeholder="Enter 2-character code"
                   [class.is-invalid]="brandCode.invalid && brandCode.touched || isDuplicate">
            <div class="invalid-feedback" *ngIf="brandCode.invalid && brandCode.touched">
              Code is required
            </div>
            <div class="text-danger small" *ngIf="isDuplicate">
              <i class="fas fa-exclamation-triangle me-1"></i>
              This code is already added
            </div>
          </div>

          <div class="mb-3">
            <label for="supplier" class="form-label fw-semibold">
              <i class="fas fa-truck me-1 text-primary"></i>
              Supplier <span class="text-danger">*</span>
            </label>
            <input [(ngModel)]="keySupplier"
                   [typeahead]="suppliers"
                   (typeaheadLoading)="loadSuppliers()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="name"
                   placeholder="Search and select supplier"
                   autocomplete="off"
                   class="form-control"
                   id="supplier" name="supplier"
                   required>
          </div>

          <div class="form-check mb-4">
            <input class="form-check-input" id="check3" name="check3" type="checkbox"
                   [(ngModel)]="brand.active">
            <label class="form-check-label fw-semibold" for="check3">
              <i class="fas fa-check-circle me-1 text-success"></i>
              Active
            </label>
          </div>

          <div class="d-flex justify-content-end gap-2 pt-3 border-top">
            <button type="button" class="btn btn-outline-secondary" (click)="clear()">
              <i class="fas fa-undo me-1"></i>
              Clear
            </button>
            <button type="button" class="btn btn-warning"
                    [disabled]="!manageBrandForm.form.valid"
                    (click)="updateBrand()">
              <i class="fas fa-edit me-1"></i>
              Update
            </button>
            <button type="submit" class="btn btn-success"
                    [disabled]="!manageBrandForm.form.valid || !selectedSupplier.id">
              <i class="fas fa-save me-1"></i>
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
