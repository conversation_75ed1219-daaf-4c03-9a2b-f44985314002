<div class="container-fluid py-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="h3 fw-bold mb-1 text-primary">
        <i class="fas fa-home me-2"></i>
        Welcome back, {{ user?.firstName || user?.username || 'User' }}!
      </h2>
      <p class="text-muted mb-0">Here's everything you can do with Viganana</p>
    </div>
    <div class="d-flex align-items-center">
      <button class="btn btn-outline-primary" (click)="toggleSidebar()">
        <i class="fas fa-chevron-down me-2"></i>Menu View
      </button>
    </div>
  </div>

  <div class="row g-4">
    <div class="col-lg-8">
      <div class="mb-4">
        <h4 class="mb-2"><i class="fas fa-th-large me-2"></i>Select a Module</h4>
        <p class="text-muted mb-3">Click on any module below to view its available functions</p>

        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-3">
          <div class="col" *ngFor="let module of modulePermissions">
            <div class="card h-100 cursor-pointer"
                 [class.border-primary]="selectedModule === module.name"
                 [class.shadow-sm]="selectedModule === module.name"
                 (click)="selectModule(module)">
              <div class="card-body d-flex align-items-center">
                <div class="me-3 fs-3 text-primary">
                  <i [class]="getModuleIcon(module.name)"></i>
                </div>
                <div>
                  <h6 class="card-title mb-0">{{ module.name }}</h6>
                  <small class="text-muted">{{ module.perms.length }} {{ module.perms.length === 1 ? 'function' : 'functions' }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="selectedModuleData" class="mt-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h5 class="mb-0"><i [class]="getModuleIcon(selectedModuleData.name)" class="me-2"></i>{{ selectedModuleData.name }} Functions</h5>
          <span class="badge bg-info-subtle text-info-emphasis rounded-pill">{{ selectedModuleData.perms.length }} available functions</span>
        </div>

        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">
          <div class="col" *ngFor="let permission of selectedModuleData.perms">
            <a [routerLink]="'../' + permission.route" class="card h-100 text-decoration-none text-dark d-block hover-shadow">
              <div class="card-body d-flex align-items-center">
                <div class="me-3 fs-4 text-secondary">
                  <i [class]="permission.iconCss"></i>
                </div>
                <div class="flex-grow-1">
                  <span class="fw-semibold">{{ permission.name }}</span>
                </div>
                <div class="ms-auto text-muted">
                  <i class="fas fa-chevron-right"></i>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>

      <div *ngIf="!selectedModuleData" class="text-center py-5 border rounded-3 bg-light-subtle mt-4">
        <i class="fas fa-hand-pointer fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Select a module above to view its functions</h5>
        <p class="text-muted">Choose a module from the grid above to see available functions</p>
      </div>
    </div>

    <div class="col-lg-4">
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="card-title mb-0"><i class="fas fa-question-circle me-2"></i>Help & Documentation</h5>
        </div>
        <div class="list-group list-group-flush">
          <a href="javascript:void(0)" class="list-group-item list-group-item-action d-flex align-items-center" (click)="openHelpLink('user-guide')">
            <i class="fas fa-book me-3"></i>
            <span>User Guide</span>
            <i class="fas fa-external-link-alt ms-auto text-muted small"></i>
          </a>
          <a href="javascript:void(0)" class="list-group-item list-group-item-action d-flex align-items-center" (click)="openHelpLink('faq')">
            <i class="fas fa-question me-3"></i>
            <span>Frequently Asked Questions</span>
            <i class="fas fa-external-link-alt ms-auto text-muted small"></i>
          </a>
          <a href="javascript:void(0)" class="list-group-item list-group-item-action d-flex align-items-center" (click)="openHelpLink('support')">
            <i class="fas fa-headset me-3"></i>
            <span>Contact Support</span>
            <i class="fas fa-external-link-alt ms-auto text-muted small"></i>
          </a>
        </div>
      </div>

      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h5 class="card-title mb-0"><i class="fab fa-youtube me-2"></i>Video Tutorials</h5>
        </div>
        <div class="list-group list-group-flush">
          <a href="javascript:void(0)" class="list-group-item list-group-item-action d-flex align-items-center" (click)="openVideoTutorial('getting-started')">
            <div class="me-3 fs-5 text-success">
              <i class="fas fa-play-circle"></i>
            </div>
            <div>
              <strong class="mb-0">Getting Started</strong>
              <div class="text-muted small">Learn the basics</div>
            </div>
          </a>
          <a href="javascript:void(0)" class="list-group-item list-group-item-action d-flex align-items-center" (click)="openVideoTutorial('inventory-management')">
            <div class="me-3 fs-5 text-success">
              <i class="fas fa-play-circle"></i>
            </div>
            <div>
              <strong class="mb-0">Inventory Management</strong>
              <div class="text-muted small">Manage your items</div>
            </div>
          </a>
          <a href="javascript:void(0)" class="list-group-item list-group-item-action d-flex align-items-center" (click)="openVideoTutorial('sales-process')">
            <div class="me-3 fs-5 text-success">
              <i class="fas fa-play-circle"></i>
            </div>
            <div>
              <strong class="mb-0">Sales Process</strong>
              <div class="text-muted small">Create invoices</div>
            </div>
          </a>
          <a href="javascript:void(0)" class="list-group-item list-group-item-action d-flex align-items-center" (click)="openVideoTutorial('reports-analytics')">
            <div class="me-3 fs-5 text-success">
              <i class="fas fa-play-circle"></i>
            </div>
            <div>
              <strong class="mb-0">Reports & Analytics</strong>
              <div class="text-muted small">Generate reports</div>
            </div>
          </a>
        </div>
      </div>

      <div class="card">
        <div class="card-header bg-info text-white">
          <h5 class="card-title mb-0"><i class="fas fa-star me-2"></i>What's New</h5>
        </div>
        <div class="list-group list-group-flush">
          <div class="list-group-item d-flex align-items-center">
            <span class="badge bg-primary-subtle text-primary-emphasis rounded-pill me-3">NEW</span>
            <div>
              <strong class="mb-0">Enhanced Reporting</strong>
              <div class="text-muted small">New filters and export options</div>
            </div>
          </div>
          <div class="list-group-item d-flex align-items-center">
            <span class="badge bg-warning-subtle text-warning-emphasis rounded-pill me-3">UPDATED</span>
            <div>
              <strong class="mb-0">Barcode Printing</strong>
              <div class="text-muted small">Improved customization options</div>
            </div>
          </div>
          <div class="list-group-item d-flex align-items-center">
            <span class="badge bg-primary-subtle text-primary-emphasis rounded-pill me-3">NEW</span>
            <div>
              <strong class="mb-0">Supplier Returns</strong>
              <div class="text-muted small">Batch return processing</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
