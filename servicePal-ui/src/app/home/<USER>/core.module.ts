import { NgModule, APP_INITIALIZER } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ModalModule } from 'ngx-bootstrap/modal';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { TypeaheadModule } from 'ngx-bootstrap/typeahead';
import { ToastrModule } from 'ngx-toastr';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { TimepickerModule } from 'ngx-bootstrap/timepicker';
import { PaginationModule } from 'ngx-bootstrap/pagination';
import { TagInputModule } from 'ngx-chips';
import { NgxPrintModule } from 'ngx-print';
import { ConfirmationPopoverModule } from 'angular-confirmation-popover';
import { JwtInterceptor } from '../../helper/jwt.interceptor';
import { ErrorInterceptor } from '../../helper/error.interceptor';
import { routeParams } from './core-routing.module';
import {NgxBarcode6Module} from 'ngx-barcode6';

@NgModule({
  declarations: [
    routeParams
  ],
  imports: [
    CommonModule,
    FormsModule,
    HttpClientModule,
    RouterModule,
    ModalModule.forRoot(),
    BsDatepickerModule.forRoot(),
    TypeaheadModule.forRoot(),
    ToastrModule.forRoot(),
    BsDropdownModule.forRoot(),
    TimepickerModule.forRoot(),
    PaginationModule.forRoot(),
    TagInputModule,
    NgxPrintModule,
    NgxBarcode6Module,
    ConfirmationPopoverModule.forRoot({
      confirmButtonType: 'danger',
      popoverTitle: 'Confirmation',
      popoverMessage: 'Are you sure?'
    })
  ],
  exports: [
    CommonModule,
    FormsModule,
    HttpClientModule,
    RouterModule,
    ModalModule,
    BsDatepickerModule,
    TypeaheadModule,
    ToastrModule,
    BsDropdownModule,
    TimepickerModule,
    PaginationModule,
    TagInputModule,
    NgxPrintModule,
    NgxBarcode6Module,
    ConfirmationPopoverModule,
    routeParams
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    DatePipe
  ]
})
export class CoreModule {}
