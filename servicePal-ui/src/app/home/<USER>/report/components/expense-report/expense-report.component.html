<div class="card">
  <div class="card-header">
    <strong>Expense Report</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-2 p-0 pe-1">
        <select type="text" required #duration="ngModel" [class.is-invalid]="duration.invalid && duration.touched"
                class="form-control" id="duration" [(ngModel)]="selectedDuration.id" name="duration"
                (ngModelChange)="filterByDuration()">
          <option *ngFor="let filter of durationFilter" [value]="filter.id">{{filter.value}}</option>
        </select>
      </div>

      <div class="col-md-2 p-0 pe-1">
        <input [(ngModel)]="keyExpenseCat"
               [typeahead]="expenseCatList"
               (typeaheadLoading)="loadExpenseCat()"
               (typeaheadOnSelect)="filterByExpenseCat($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="name"
               placeholder="Expense Category"
               autocomplete="off"
               size="16"
               class="form-control" name="searchExpenseCat">
      </div>

      <div class="col-md-2 p-0 pe-1">
        <input [(ngModel)]="keyExpenseType"
               [typeahead]="expenseTypeList"
               (typeaheadLoading)="loadExpenseTypes()"
               (typeaheadOnSelect)="filterByExpenseType($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="name"
               placeholder="Expense Type"
               autocomplete="off"
               size="16"
               class="form-control" name="searchExpenseType">
      </div>

      <div class="col-md-2 p-0 pe-1">
        <input [(ngModel)]="keyEmpSearch"
               [typeahead]="empSearchList"
               (typeaheadLoading)="searchEmployee()"
               (typeaheadOnSelect)="filterByEmployee($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="name"
               placeholder="Responsible Person"
               autocomplete="off"
               size="16"
               class="form-control" name="searchEmp">
      </div>

      <div class="col-md-2 p-0 pe-1">
        <input required #startDate="ngModel" type="text" name="startDate" id="startDate"
               [(ngModel)]="sDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="Enter Job Date">
      </div>
      <div class="col-md-2 p-0">
        <input required #endDate="ngModel" type="text" name="endDate" id="endDate"
               [(ngModel)]="eDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="Enter Job Date" (ngModelChange)="findExpenses(sDate,eDate)">
      </div>
    </div>

    <div class="row g-3 mt-2" id="print-income-div">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Date</th>
          <th scope="col">Category</th>
          <th scope="col">Type</th>
          <th scope="col">Responsible Person</th>
          <th scope="col">Amount</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let ex of expenses,let i = index"
            (click)="selectExp(ex,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{ex.date | date:'short'}}</td>
          <td>{{ex.type.category.name}}</td>
          <td>{{ex.type.name}}</td>
          <td>{{ex.responsiblePerson != null ? ex.responsiblePerson.name : "N/A"}}</td>
          <td>{{ex.amount | number : '1.2-2'}}</td>
        </tr>
        </tbody>
      </table>
      <label class="ms-1 mt-3 fw-bold">Total Amount</label>
      <label class="mt-3 ms-3 fw-bold">{{totalAmount | number : '1.2-2'}}</label>
    </div>
    <div class="row g-3">
      <div class="col-md-12">
        <pagination class="pagination-sm justify-content-center mt-2"
                    [totalItems]="collectionSize"
                    [maxSize]="maxSize"
                    [boundaryLinks]="true"
                    [(ngModel)]="page"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>
    <div class="row g-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-end ms-2" printSectionId="print-income-div" ngxPrint
                [useExistingCss]="true" printTitle="Expense Report">Print
        </button>
        <button type="button" class="btn btn-danger float-end ms-2" (click)="viewDetail()">View Detail</button>
      </div>
    </div>
  </div>
</div>
