<div class="card">
  <div class="card-header">
    <strong>MANAGE SUPPLIERS</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <div class="row">
          <input [(ngModel)]="keySupplier"
                 [typeahead]="suppliers"
                 (typeaheadLoading)="loadSuppliers()"
                 (typeaheadOnSelect)="setSelectedSupplier($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="name"
                 placeholder="Search Supplier"
                 autocomplete="off"
                 size="16"
                 required
                 class="form-control m-" name="supplier">
        </div>
        <div class="row mt-3">
          <table class="table table-striped">
            <thead>
            <tr style="text-align: center">
              <th scope="col">Supplier Name</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let supplier of suppliers,let i=index" (click)="supplierDetail(supplier)"
                [class.active]="i === selectedRow">
              <td>{{supplier.name}}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="row">
          <pagination class="pagination-sm justify-content-center mt-2" [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>

      <div class="col-md-6 mt-2">
        <form (ngSubmit)="saveSupplier(); manageSuppliers.reset()" #manageSuppliers="ngForm">
          <div class="row">
            <div class="col-md-6 form-group">
              <label>Supplier Name</label>
              <input type="text" required #sName="ngModel" [class.is-invalid]="sName.invalid && sName.touched"
                     class="form-control" id="sName" [(ngModel)]="supplier.name" name="sName"
                     placeholder="Supplier Name">
              <div *ngIf="sName.errors && (sName.invalid || sName.touched)">
                <small class="text-danger" [class.d-none]="sName.valid || sName.untouched">*Supplier Name is required
                </small>
              </div>
            </div>
            <div class="col-md-6 form-group">
              <label>Registration Number</label>
              <input type="text" required #reg="ngModel" [class.is-invalid]="reg.invalid && reg.touched"
                     class="form-control" id="reg" [(ngModel)]="supplier.regNo" name="reg"
                     placeholder="Registration Number">
              <div *ngIf="reg.errors && (reg.invalid || reg.touched)">
                <small class="text-danger" [class.d-none]="reg.valid || reg.untouched">*registration number is required
                </small>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 form-group">
              <label>Address</label>
              <input type="text"
                     class="form-control" id="address1" name="address1" #address1="ngModel"
                     placeholder="Address 1" [(ngModel)]="supplier.address"
                     [class.is-invalid]="address1.invalid && address1.touched">
              <div *ngIf="address1.errors && (address1.invalid || address1.touched)">
                <small class="text-danger" [class.d-none]="address1.valid || address1.untouched">*address is required
                </small>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 form-group">
              <label>Telephone Number 1</label>
              <input type="text" class="form-control" placeholder="Telephone 1" required #phone1="ngModel" name="phone1"
                     id="phone1"
                     [(ngModel)]="supplier.telephone1" class="form-control" pattern="^\d{10}$"
                     [class.is-invalid]="phone1.invalid && phone1.touched">
              <div *ngIf="phone1.errors && (phone1.invalid || phone1.touched)">
                <small class="text-danger" [class.d-none]="phone1.valid || phone1.untouched">*Telephone number is
                  required
                </small>
              </div>

            </div>
            <div class="col-md-6 form-group">
              <label>Telephone Number 2</label>
              <input type="text" class="form-control" placeholder="Telephone Number" name="tel"
                     id="tel" class="form-control" pattern="^\d{10}$" [(ngModel)]="supplier.telephone2">
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 form-group">
              <label>E-mail</label>
              <input type="text" class="form-control" id="email" placeholder="Enter Email"
                     [(ngModel)]="supplier.email" name="email"
                     pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$">
            </div>
            <div class="col-md-6 form-group">
              <label>Balance</label>
              <input type="text" #bal="ngModel" [class.is-invalid]="bal.invalid && bal.touched"
                     class="form-control" id="bal" [(ngModel)]="supplier.balance" name="bal"
                     placeholder="Balance">
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 form-group">
              <label>Contact Person Name</label>
              <input type="text"
                     class="form-control" id="pName" name="pName"
                     placeholder="Contact Person Name" [(ngModel)]="supplier.contactPersonName">
            </div>
            <div class="col-md-6 form-group">
              <label> Contact Person Telephone</label>
              <input type="tel" class="form-control" placeholder=" Contact Person Telephone"
                     name="CPphone" id="CPphone" class="form-control" pattern="^\d{10}$"
                     [(ngModel)]="supplier.contactPersonTelephone">
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 mt-5">
              <div class="form-check checkbox me-2">
                <input class="form-check-input" id="check" name="check" type="checkbox" [(ngModel)]="supplier.active">
                <label class="form-check-label" for="check">Is Active</label>
              </div>
            </div>
          </div>
          <div class="row float-end">
            <button type="button" class="btn btn-warning" [disabled]="!manageSuppliers.form.valid" (click)="Clear()">
              clear
            </button>
            <button type="submit" [disabled]="!manageSuppliers.form.valid" class="btn btn-success ms-2">save</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
