<div class="card">
  <div class="card-header">
    <strong>Manage Sales Invoices</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="keyInvoiceNo"
                 placeholder="search By Invoice No"
                 autocomplete="off" size="16"
                 class="form-control" name="invNo" (change)="setSearchFilter('inv')">
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="keyJobNo"
                 placeholder="search By Job No"
                 autocomplete="off" size="16"
                 class="form-control" name="jobNo" (change)="setSearchFilter('job')">
        </div>
      </div>
      <div class="col-md-3">
        <div class="mb-3 input-group">
          <input [(ngModel)]="keyCustomerNicBr"
                 placeholder="search By Customer"
                 autocomplete="off" size="16"
                 class="form-control" name="invNo" (change)="setSearchFilter('cust')">
                  <button class="btn btn-success fa fa-search" (click)="searchCustomer()"
                          type="button"></button>
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <select class="form-control" name="typeFilter" [(ngModel)]="invSaleType"
                  (change)="setSearchFilter('saleType')">
            <option ngValue="0">Direct Invoice</option>
            <option ngValue="1">Job Invoice</option>
          </select>
        </div>
      </div>
      <div class="col-md-1">
        <button class="btn btn-primary" (click)="searchSi()">Search</button>
      </div>
    </div>

    <div class="row g-3 mt-2">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Invoice No</th>
          <th scope="col">Customer Name</th>
          <th scope="col">Created Date</th>
          <th scope="col">Amount</th>
          <th scope="col">Balance</th>
          <th scope="col">Status</th>
          <th scope="col">Paid Date</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let si of sis,let i = index"
            (click)="selectSi(si,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{si.invoiceNo}}</td>
          <td>{{si.customerName}}</td>
          <td>{{si.date | date:'short': '+530'}}</td>
          <td>{{si.totalAmount | number : '1.2-2'}}</td>
          <td>{{si.balance | number: '1.2-2'}}</td>
          <td>{{si.status.value}}</td>
          <td>{{si.paidDate}}</td>
        </tr>
        </tbody>
      </table>
      <div class="row g-3">
        <div class="col-md-12">
          <pagination class="pagination-sm justify-content-center mt-2"
                      [totalItems]="collectionSize"
                      [maxSize]="maxSize"
                      [boundaryLinks]="true"
                      [(ngModel)]="page"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
    <div class="row g-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-start" mwlConfirmationPopover (confirm)="amend()">Amend</button>
        <button type="button" class="btn btn-danger float-end ms-2" (click)="print()">View</button>
        <button type="button" class="btn btn-danger float-end ms-2" [disabled]="selectedSi.balance == 0"
                (click)="payBalance()">Pay Balance
        </button>
      </div>
    </div>
  </div>
</div>
