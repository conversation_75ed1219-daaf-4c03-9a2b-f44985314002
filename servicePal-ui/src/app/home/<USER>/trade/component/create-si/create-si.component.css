/* Table Styling */
.table-height {
  height: 200px;
  overflow-y: auto;
}

.table-active {
  background-color: #0d6efd !important;
  color: white !important;
}

.table-active:hover {
  background-color: #0b5ed7 !important;
  color: white !important;
}

/* Compact Layout Styling */
.form-control-sm, .form-select-sm {
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
}

.form-label.small {
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

/* Sticky table header */
.sticky-top {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Compact spacing */
.p-2 {
  padding: 0.5rem !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

/* Button sizing */
.btn {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Custom Card Styling */
.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-radius: 8px;
}

.card-header {
  border-radius: 8px 8px 0 0 !important;
  border: none;
}

/* Cursor Pointer */
.cursor-pointer {
  cursor: pointer;
}

/* Form Controls */
.form-control:focus,
.form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Button Styling */
.btn {
  border-radius: 6px;
  font-weight: 500;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 1.1rem;
}

/* Header Styling */
.bg-primary {
  background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%) !important;
}

/* Sidebar Styling */
.border-end {
  border-right: 2px solid #e9ecef !important;
}

/* Input Group Styling */
.input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
}

/* Animation for loading */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card {
  animation: fadeIn 0.3s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .table-height {
    height: 40vh;
  }

  .btn-lg {
    padding: 10px 20px;
    font-size: 1rem;
  }

  .card-body {
    padding: 1rem;
  }
}


