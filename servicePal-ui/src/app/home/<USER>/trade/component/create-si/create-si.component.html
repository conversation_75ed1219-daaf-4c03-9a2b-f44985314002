<div class="container-fluid vh-100 bg-light">
  <!-- Compact Header -->
  <div class="bg-primary text-white py-2 px-3">
    <div class="d-flex justify-content-between align-items-center">
      <h5 class="mb-0 fw-bold">
        <i class="fa fa-receipt me-2"></i>Sales Invoice
      </h5>
      <div class="btn-group btn-group-sm">
        <button type="button" class="btn btn-outline-light" (click)="openCustomer()" title="Customer">
          <i class="fa fa-person"></i>
        </button>
        <button type="button" class="btn btn-outline-light" (click)="openStock()" title="Stock">
          <i class="fa fa-luggage-cart"></i>
        </button>
        <button type="button" class="btn btn-outline-light" (click)="openPastInvoice()" title="History">
          <i class="fa fa-history"></i>
        </button>
        <button type="button" class="btn btn-outline-light" (click)="openCashier()" title="Cashier">
          <i class="fa fa-money-bill"></i>
        </button>
        <button type="button" class="btn btn-outline-light" routerLink="../home/<USER>" (click)="closeFullscreen()" title="Home">
          <i class="fa fa-home"></i>
        </button>
      </div>
    </div>
  </div>
  <!-- Main Content -->
  <div class="p-3">
    <!-- Top Controls Row -->
    <div class="row g-2 mb-3">
      <!-- Sale Type -->
      <div class="col-md-3">
        <div class="bg-white p-2 rounded border">
          <label class="form-label fw-bold text-primary mb-1">Sale Type</label>
          <div *ngFor="let type of saleTypeList, let i = index">
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="salesType" [id]="type.id" [value]="type.value"
                     [defaultChecked]="i === 0" required (click)="setSaleType(type.id)">
              <label class="form-check-label small" [for]="type.id">{{ type.value }}</label>
            </div>
          </div>
        </div>
      </div>

      <!-- User Info -->
      <div class="col-md-2">
        <div class="bg-white p-2 rounded border text-center">
          <div class="text-primary fw-bold">{{ user.username }}</div>
          <small class="text-muted">Counter {{ user.counter }}</small>
        </div>
      </div>
      <!-- Job Information (Non-Direct Mode) -->
      <div class="col-md-7" *ngIf="directMode === false">
        <div class="bg-white p-2 rounded border">
          <label class="form-label fw-bold text-info mb-1">Job Information</label>
          <div class="row g-2">
            <div class="col-3">
              <label class="form-label small mb-1">Job No</label>
              <input [ngModel]="job.jobNo" class="form-control form-control-sm" name="jobNo" disabled>
            </div>
            <div class="col-3">
              <label class="form-label small mb-1">Customer</label>
              <input [ngModel]="job.customer.name" class="form-control form-control-sm" name="customer" disabled>
            </div>
            <div class="col-3">
              <label class="form-label small mb-1">Machine</label>
              <input [ngModel]="job.machine.modelNo" class="form-control form-control-sm" name="modelNo" disabled>
            </div>
            <div class="col-3">
              <label class="form-label small mb-1">Technician</label>
              <tag-input [ngModel]="job.assignments" [identifyBy]="'id'" [displayBy]="'name'"
                         [hideForm]="true" name="techList" [editable]="false"></tag-input>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Item Search Row -->
    <div class="row g-2 mb-3" *ngIf="directMode === true">
      <div class="col-12">
        <div class="bg-white p-2 rounded border">
          <label class="form-label fw-bold text-success mb-2">Add Items</label>
          <div class="row g-2">
            <!-- Barcode field -->
            <div class="col-md-2">
              <label class="form-label small mb-1">Barcode</label>
              <input [(ngModel)]="keyItemSearch"
                     [typeahead]="itemSearchList"
                     (typeaheadLoading)="searchItems()"
                     (typeaheadOnSelect)="setSelectedItem($event)"
                     [typeaheadOptionsLimit]="7"
                     typeaheadOptionField="barcode"
                     autocomplete="off"
                     size="16" #barcode (keydown.enter)="gotoPayment()"
                     class="form-control form-control-sm"
                     name="searchItem"
                     placeholder="Scan barcode...">
            </div>
            <!-- Item name field -->
            <div class="col-md-4">
              <label class="form-label small mb-1">Item Name</label>
              <input [(ngModel)]="keyItemNameSearch"
                     [typeahead]="itemNameSearchList"
                     (typeaheadLoading)="searchItemsByName()"
                     (typeaheadOnSelect)="setSelectedItem($event)"
                     [typeaheadOptionsLimit]="7"
                     typeaheadOptionField="itemName"
                     autocomplete="off"
                     size="16"
                     class="form-control form-control-sm"
                     name="searchItem"
                     placeholder="Search by name...">
            </div>
            <!-- Price field -->
            <div class="col-md-2">
              <label class="form-label small mb-1">Price</label>
              <input type="number" required #price="ngModel" class="form-control form-control-sm" id="price"
                     name="price" [class.is-invalid]="price.invalid && price.touched"
                     [(ngModel)]="sPrice" #sellingPrice (keydown.arrowRight)="focusQty()"
                     placeholder="0.00">
            </div>
            <!-- Discount field -->
            <div class="col-md-2">
              <label class="form-label small mb-1 d-flex align-items-center">
                Disc
                <span class="ms-1 cursor-pointer" (click)="selectedDiscountMethod = selectedDiscountMethod === 'Percentage' ? 'Flat' : 'Percentage'; setDiscountMethod()">
                  <i class="fa fa-percent" *ngIf="selectedDiscountMethod === 'Percentage'"></i>
                  <i class="fa fa-minus" *ngIf="selectedDiscountMethod === 'Flat'"></i>
                </span>
              </label>
              <input type="number" class="form-control form-control-sm"
                     [(ngModel)]="discount" (ngModelChange)="calculateTotal()"
                     placeholder="0">
            </div>
            <!-- Quantity field with Return checkbox -->
            <div class="col-md-2">
              <div class="d-flex justify-content-between align-items-center mb-1">
                <label class="form-label small mb-0">Qty</label>
                <div class="form-check form-check-inline m-0 p-0">
                  <input class="form-check-input" type="checkbox" id="returnCheck" name="returnCheck">
                  <label class="form-check-label small" for="returnCheck">Return</label>
                </div>
              </div>
              <input type="number" required #qty="ngModel" #quantity class="form-control" id="qty"
                     name="qty" [class.is-invalid]="qty.invalid && qty.touched"
                     [(ngModel)]="itemQty" (keydown.enter)="checkAvailability()"
                     (keydown.arrowLeft)="focusPrice()"
                     placeholder="1">
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Items Table -->
    <div class="row g-2 mb-3">
      <div class="col-12">
        <div class="bg-white rounded border">
          <div class="bg-secondary text-white p-2 rounded-top">
            <strong>Invoice Items</strong>
          </div>
          <div class="table-responsive" style="height: 200px; overflow-y: auto;">
            <table class="table table-hover table-sm mb-0">
              <thead class="table-light sticky-top">
                <tr>
                  <th style="width: 350px;">Barcode</th>
                  <th>Item</th>
                  <th class="text-center" style="width: 100px;">Qty</th>
                  <th class="text-end" style="width: 150px;">Price</th>
                  <th style="width: 50px;"></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let siRec of si.salesInvoiceRecords,let i = index"
                    (click)="selectRow(i)"
                    [class.table-active]="i === selectedSiRecordIndex"
                    class="cursor-pointer">
                  <td>{{ siRec.barcode }}</td>
                  <td>{{ siRec.itemName }}</td>
                  <td class="text-center">{{ siRec.quantity }}</td>
                  <td class="text-end">{{ siRec.price | number }}</td>
                  <td>
                    <button class="btn btn-danger btn-sm" (click)="removeRow(i)">X</button>
                  </td>
                </tr>
                <tr *ngIf="si.salesInvoiceRecords.length === 0">
                  <td colspan="5" class="text-center text-muted py-3">
                    <i class="fa fa-inbox me-1"></i>No items added yet
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <!-- Customer and Totals Row -->
    <div class="row g-2 mb-3">
      <!-- Customer section -->
      <div class="col-md-3">
        <div class="bg-white p-2 rounded border">
          <label class="form-label fw-bold text-info mb-2">Customer</label>
          <div class="input-group input-group-sm">
            <input [(ngModel)]="keyCustomerSearch"
                   [typeahead]="customerSearchList"
                   (typeaheadLoading)="searchCustomers()"
                   (typeaheadOnSelect)="setSelectedCustomer($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="name"
                   autocomplete="off"
                   placeholder="Search customer"
                   class="form-control" name="searchCustomer">
            <button class="btn btn-outline-primary" (click)="newCustomer()" type="button">
              <i class="fa fa-plus"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Totals section -->
      <div class="col-md-9">
        <div class="bg-white p-2 rounded border">
          <label class="form-label fw-bold text-warning mb-2">Totals & Payment</label>
          <div class="row g-2">
            <div class="col-md-2">
              <label class="form-label small mb-1">Sub Total</label>
              <input class="form-control form-control-sm bg-light" [ngModel]="si.subTotal | number" readonly>
            </div>
            <div class="col-md-2">
              <label class="form-label small mb-1">Total Discount</label>
              <input class="form-control form-control-sm" [(ngModel)]="si.totalDiscount" (ngModelChange)="calculateTotal()">
            </div>
            <div class="col-md-2">
              <label class="form-label small mb-1 text-primary">Total</label>
              <input class="form-control form-control-sm bg-primary text-white fw-bold" [ngModel]="si.totalAmount | number" readonly>
            </div>
            <div class="col-md-2">
              <label class="form-label small mb-1">Payment</label>
              <input class="form-control form-control-sm" [(ngModel)]="si.payment" placeholder="Amount"
                     #payment (ngModelChange)="calculateBalance()" (keyup.enter)="saveByEnter()">
            </div>
            <div class="col-md-2">
              <label class="form-label small mb-1">Method</label>
              <select class="form-select form-select-sm" (change)="setPaymentMethod($event)" name="paymentMethodSelected"
                      [(ngModel)]="paymentMethodId" required #paymentMethodSelect="ngModel"
                      [class.is-invalid]="paymentMethodSelect.invalid && paymentMethodSelect.touched">
                <option value="">Select</option>
                <option *ngFor="let method of paymentMethods" [value]="method.id">
                  {{ method.value }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label small mb-1 text-danger">Balance</label>
              <input class="form-control form-control-sm bg-danger text-white fw-bold" [ngModel]="si.cashBalance | number" readonly>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Action Buttons -->
    <div class="row g-2">
      <div class="col-12">
        <div class="bg-white p-2 rounded border d-flex justify-content-between align-items-center">
          <div>
            <button type="button" class="btn btn-outline-danger"
                    *ngIf="directMode === true" (click)="removeRow()">
              <i class="fa fa-trash me-1"></i>Remove Item
            </button>
          </div>
          <div class="d-flex gap-2">
            <button type="button" class="btn btn-primary"
                    (click)="save(true)" [disabled]="isProcessing">
              <i class="fa fa-print me-1"></i>Print
              <span class="spinner-border spinner-border-sm ms-1" *ngIf="isProcessing"></span>
            </button>
            <button type="button" class="btn btn-success"
                    (click)="save(false)" [disabled]="isProcessing">
              <i class="fa fa-save me-1"></i>Save
              <span class="spinner-border spinner-border-sm ms-1" *ngIf="isProcessing"></span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
