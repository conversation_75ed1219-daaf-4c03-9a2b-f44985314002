<div>
  <div class="card">
    <div class="card-header">
      <strong>Manage Machine Categories</strong>

    </div>
    <div class="card-body">
      <form (ngSubmit)="save(); catForm.reset()" #catForm="ngForm">
        <div class="row">
          <div class="col-md-6">
            <input type="text" class="form-control" id="uom" name="uom" placeholder="Search Categories">
            <br>
            <table class="table">
              <thead>
              <tr style="text-align: center">
                <th scope="col">Name</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let cat of machineCategories,let i=index" (click)="setSelectedCat(cat,i)"
                  [class.active]="i === selectedRow">
                <td>{{cat.name}}</td>
              </tr>
              </tbody>
            </table>
            <div class="row">
              <div class="col-xs-12 col-12 ">
                <pagination class="pagination-sm justify-content-center mt-2"
                  [totalItems]="collectionSize"
                  [(ngModel)]="page"
                  [boundaryLinks]="true"
                  [maxSize]="10"
                  (pageChanged)="pageChanged($event)"
                  [ngModelOptions]="{standalone: true}">
                </pagination>
              </div>
            </div>

          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label>Category Name</label>
              <input type="text" required #catName="ngModel" [class.is-invalid]="catName.invalid && catName.touched"
                     class="form-control" id="catName" [(ngModel)]="machineCategory.name" name="catName"
                     placeholder="Category Name">
              <div *ngIf="catName.errors && (catName.invalid || catName.touched)">
                <small class="text-danger" [class.d-none]="catName.valid || catName.untouched">*Category Name is
                  required
                </small>
              </div>
            </div>

            <div class="form-check checkbox me-2">
              <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                     [(ngModel)]="machineCategory.active">
              <label class="form-check-label" for="check3">Active</label>
            </div>

            <div class="row float-end ">
              <div class="me-3">
                <button type="submit" class="btn btn-success" [disabled]="!catForm.form.valid">save</button>
              </div>

              <div class="me-3">
                <button type="button" class="btn btn-warning" [disabled]="!catForm.form.valid" (click)="clearAll()">
                  clear
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
