<div class="card">
  <div class="card-header">
    <strong>View All Machines</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keySerialSearch"
                 [typeahead]="machineSearchList"
                 (typeaheadLoading)="searchMachinesBySerial()"
                 (typeaheadOnSelect)="addFilteredRecord($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="serialNo"
                 autocomplete="off"
                 placeholder="Search By Serial No"
                 class="form-control" name="serialSearch">
        </div>
      </div>
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keyCustomerSearch"
                 [typeahead]="customerSearchList"
                 (typeaheadLoading)="searchCustomers()"
                 (typeaheadOnSelect)="setSelectedCustomer($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="name"
                 placeholder="Search By Customer"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="customerSearch">
        </div>
      </div>
    </div>

    <div class="modal-body" (load)="findAllMachines()">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Serial No</th>
          <th scope="col">Customer</th>
          <th scope="col">Brand Name</th>
          <th scope="col">Model No</th>
          <th scope="col">Service Date</th>
          <th scope="col">Status</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let mcn of agreements,let i = index"
            (click)="itemRecord(mcn,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{mcn.machineSerial}}</td>
          <td>{{mcn.customer.name}}</td>
          <td>{{mcn.brand.name}}</td>
          <td>{{mcn.model}}</td>
          <td>{{mcn.date}}</td>
          <td>{{mcn.status.name}}</td>
        </tr>
        </tbody>
      </table>
      <div class="row">
        <div class="col-md-12">
          <pagination class="pagination-sm justify-content-center mt-2"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      [boundaryLinks]="true"
                      [maxSize]="maxsize"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
      {{collectionSize + ' ' + maxsize}}
      <div class="row d-flex">
        <button type="button" class="btn btn-danger float-end" (click)="edit()">Edit</button>
      </div>
    </div>
  </div>
</div>

