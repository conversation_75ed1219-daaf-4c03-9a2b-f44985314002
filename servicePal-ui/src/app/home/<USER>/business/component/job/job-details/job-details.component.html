<div class="container-fluid">
  <div class="card shadow-lg">
    <div class="card-header bg-primary text-white">
      <div class="d-flex justify-content-between align-items-center">
        <h4 class="mb-0">
          <i class="fa fa-clipboard-list me-2"></i>Comprehensive Job Details
        </h4>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
    </div>
    <div class="card-body">

      <!-- Job Information Section -->
      <div class="card mb-4">
        <div class="card-header bg-info text-white">
          <h6 class="mb-0">
            <i class="fa fa-info-circle me-2"></i>Job Information
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-3">
              <label class="form-label fw-bold text-primary">
                <i class="fa fa-hashtag me-1"></i>Job Number
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.jobNo || 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-3">
              <label class="form-label fw-bold text-primary">
                <i class="fa fa-file-text me-1"></i>Work Order No
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.woNumber || 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-3">
              <label class="form-label fw-bold text-primary">
                <i class="fa fa-calendar me-1"></i>Created Date
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.jobDate ? (job.jobDate | date:'medium') : 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-3">
              <label class="form-label fw-bold text-primary">
                <i class="fa fa-info-circle me-1"></i>Job Status
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="badge bg-primary fs-6">{{ job.jobStatus?.value || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Customer Information Section -->
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h6 class="mb-0">
            <i class="fa fa-user me-2"></i>Customer Information
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-4">
              <label class="form-label fw-bold text-success">
                <i class="fa fa-user me-1"></i>Customer Name
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.customer?.name || 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-4">
              <label class="form-label fw-bold text-success">
                <i class="fa fa-id-card me-1"></i>NIC/BR Number
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.customer?.nicBr || 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-4">
              <label class="form-label fw-bold text-success">
                <i class="fa fa-phone me-1"></i>Contact Numbers
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">
                  {{ job.customer?.telephone1 || 'N/A' }}
                  <span *ngIf="job.customer?.telephone2"> / {{ job.customer.telephone2 }}</span>
                </span>
              </div>
            </div>

            <div class="col-md-8">
              <label class="form-label fw-bold text-success">
                <i class="fa fa-map-marker-alt me-1"></i>Customer Address
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.customer?.address || 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-4">
              <label class="form-label fw-bold text-success">
                <i class="fa fa-envelope me-1"></i>Email Address
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.customer?.email || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Machine Information Section -->
      <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
          <h6 class="mb-0">
            <i class="fa fa-cog me-2"></i>Machine Information
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-3">
              <label class="form-label fw-bold text-warning">
                <i class="fa fa-industry me-1"></i>Machine Brand
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.machine?.brand?.name || 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-3">
              <label class="form-label fw-bold text-warning">
                <i class="fa fa-tag me-1"></i>Model Number
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.machine?.modelNo || 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-3">
              <label class="form-label fw-bold text-warning">
                <i class="fa fa-barcode me-1"></i>Serial Number
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.machine?.serialNo || 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-3">
              <label class="form-label fw-bold text-warning">
                <i class="fa fa-list me-1"></i>Category
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.machine?.machineCategory?.name || 'N/A' }}</span>
              </div>
            </div>

            <div class="col-md-3" *ngIf="job.machine?.dateOfSale">
              <label class="form-label fw-bold text-warning">
                <i class="fa fa-calendar-alt me-1"></i>Date of Sale
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.machine.dateOfSale | date:'mediumDate' }}</span>
              </div>
            </div>

            <div class="col-md-6" *ngIf="job.warehouse">
              <label class="form-label fw-bold text-warning">
                <i class="fa fa-warehouse me-1"></i>Spare Parts Location
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.warehouse?.name || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Job Progress & Team Section -->
      <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
          <h6 class="mb-0">
            <i class="fa fa-users me-2"></i>Job Progress & Team
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label fw-bold text-secondary">
                <i class="fa fa-users me-1"></i>Assigned Technicians
              </label>
              <div class="p-2 bg-light rounded border">
                <div *ngIf="job.assignments && job.assignments.length > 0; else noTechnicians">
                  <span *ngFor="let tech of job.assignments; let last = last" class="badge bg-secondary me-1 mb-1">
                    <i class="fa fa-user me-1"></i>{{ tech.name }}
                  </span>
                </div>
                <ng-template #noTechnicians>
                  <span class="text-muted">No technicians assigned</span>
                </ng-template>
              </div>
            </div>

            <div class="col-md-6">
              <label class="form-label fw-bold text-secondary">
                <i class="fa fa-user-check me-1"></i>Inspected By
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.inspectedBy?.name || 'Not inspected yet' }}</span>
              </div>
            </div>

            <div class="col-md-6">
              <label class="form-label fw-bold text-secondary">
                <i class="fa fa-calendar-check me-1"></i>Completed Date
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.completedDate ? (job.completedDate | date:'medium') : 'Not completed yet' }}</span>
              </div>
            </div>

            <div class="col-md-6" *ngIf="job.showRoom">
              <label class="form-label fw-bold text-secondary">
                <i class="fa fa-store me-1"></i>Showroom
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold">{{ job.showRoom?.name || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Job Details & Issues Section -->
      <div class="card mb-4">
        <div class="card-header bg-danger text-white">
          <h6 class="mb-0">
            <i class="fa fa-exclamation-triangle me-2"></i>Job Details & Issues
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-4" *ngIf="job.defect">
              <label class="form-label fw-bold text-danger">
                <i class="fa fa-bug me-1"></i>Defects Found
              </label>
              <div class="p-3 bg-light rounded border">
                <p class="mb-0">{{ job.defect }}</p>
              </div>
            </div>

            <div class="col-md-4" *ngIf="job.damages">
              <label class="form-label fw-bold text-danger">
                <i class="fa fa-exclamation-triangle me-1"></i>Damages
              </label>
              <div class="p-3 bg-light rounded border">
                <p class="mb-0">{{ job.damages }}</p>
              </div>
            </div>

            <div class="col-md-4" *ngIf="job.lessParts">
              <label class="form-label fw-bold text-danger">
                <i class="fa fa-minus-circle me-1"></i>Missing Parts
              </label>
              <div class="p-3 bg-light rounded border">
                <p class="mb-0">{{ job.lessParts }}</p>
              </div>
            </div>

            <div class="col-md-12" *ngIf="job.remark">
              <label class="form-label fw-bold text-danger">
                <i class="fa fa-comment me-1"></i>Additional Remarks
              </label>
              <div class="p-3 bg-light rounded border">
                <p class="mb-0">{{ job.remark }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Financial Information Section -->
      <div class="card mb-4" *ngIf="job.advancePayment || job.jobEstimate">
        <div class="card-header bg-success text-white">
          <h6 class="mb-0">
            <i class="fa fa-dollar-sign me-2"></i>Financial Information
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-6" *ngIf="job.advancePayment">
              <label class="form-label fw-bold text-success">
                <i class="fa fa-money-bill me-1"></i>Advance Payment
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold fs-5 text-success">{{ job.advancePayment | currency }}</span>
              </div>
            </div>

            <div class="col-md-6" *ngIf="job.jobEstimate?.totalAmount">
              <label class="form-label fw-bold text-success">
                <i class="fa fa-calculator me-1"></i>Estimated Total
              </label>
              <div class="p-2 bg-light rounded border">
                <span class="fw-bold fs-5 text-success">{{ job.jobEstimate.totalAmount | currency }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-end gap-2">
            <button type="button" class="btn btn-primary" *ngIf="job.jobEstimate">
              <i class="fa fa-calculator me-2"></i>View Estimate
            </button>
            <button type="button" class="btn btn-success" *ngIf="job.completedDate">
              <i class="fa fa-file-invoice me-2"></i>Generate Invoice
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
