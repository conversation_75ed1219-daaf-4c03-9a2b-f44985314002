import {Component, OnInit} from '@angular/core';
import {Job} from '../../../model/job';
import {JobService} from '../../../service/job.service';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {JobDetailsComponent} from "../job-details/job-details.component";
import {Customer} from "../../../../trade/model/customer";
import {Machine} from "../../../model/machine";
import {CustomerService} from "../../../../trade/service/customer.service";
import {MachineService} from "../../../service/machine.service";
import {JobEstimateComponent} from "../job-estimate/job-estimate.component";
import {InvoiceComponent} from "../../../../trade/component/invoice/invoice.component";
import {ManageCustomerComponent} from "../../../../trade/component/customer/manage-customer/manage-customer.component";

@Component({
  standalone: false,
  selector: 'app-manage-job',
  templateUrl: './manage-job.component.html',
  styleUrls: ['./manage-job.component.css']
})
export class ManageJobComponent implements OnInit {

  jobs: Array<Job> = [];
  selectedJob: Job;

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];

  keyMachineSearch: string;
  machineSearchList: Array<Machine> = [];

  keyJobNo: string;
  keyWoNo: string;
  jobListForWo: Array<Job> = [];

  page;
  pageSize;
  collectionSize;

  selectedRow: number;
  modalRef: BsModalRef;

  constructor(private jobService: JobService, private customerService: CustomerService,
              private modalService: BsModalService, private machineService: MachineService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.findAllPendingJobs();
  }

  searchJob() {
    if (this.keyJobNo != null) {
      this.jobService.findByJobNo(this.keyJobNo).subscribe((data: Job) => {
        this.jobs = new Array<Job>();
        this.jobs.push(data);
      });
    }
    if (this.keyWoNo != null) {
      this.jobService.findByWoNo(this.keyWoNo).subscribe((data: Job) => {
        this.jobs = new Array<Job>();
        this.jobs.push(data);
      });
    }
  }

  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  searchMachines() {
    this.machineService.searchBySerialNoLike(this.keyMachineSearch).subscribe((result: Array<Machine>) => {
      return this.machineSearchList = result;
    })
  }

  setSelectedJob(event) {
    this.selectedJob = event.item;
  }

  selectJob(job, index) {
    this.selectedRow = index;
    this.selectedJob = job;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAllPendingJobs();
  }

  findAllPendingJobs() {
    this.jobService.findAllPagination(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.jobs = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  viewDetails() {
    this.modalRef = this.modalService.show(JobDetailsComponent, <ModalOptions>{class: 'modal-xxl'});
    this.modalRef.content.jobNo = this.selectedJob.jobNo;
    this.modalRef.content.initiate();
  }

  setSelectedCustomer(event) {
    this.searchByCustomer(event.item.id);
  }

  searchByCustomer(customerId) {
    this.jobService.findByCustomer(customerId).subscribe((data: Array<Job>) => {
      this.jobs = data;
    });
  }

  searchWo() {
    this.jobService.findWoNo(this.keyWoNo).subscribe((data: Array<Job>) => {
      this.jobListForWo = data;
    });
  }

  setSelectedMachine(event) {
    this.jobService.findBySerial(event.item.serialNo).subscribe((data: Array<Job>) => {
      this.jobs = data;
    });
  }

  showEstimate() {
    this.modalRef = this.modalService.show(JobEstimateComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.jobNo = this.selectedJob.jobNo;
    this.modalRef.content.findSelectedJob();
  }

  showInvoice() {
    this.modalRef = this.modalService.show(InvoiceComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.jobNo = this.selectedJob.jobNo;
    this.modalRef.content.findInvoiceByJobId();
  }

  searchCustomer() {
    this.modalRef = this.modalService.show(ManageCustomerComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.disableSetCustomer = false;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(event => {
      this.searchByCustomer(this.modalRef.content.customer.id);
      this.keyCustomerSearch = this.modalRef.content.customer.name;
    })
  }

}
