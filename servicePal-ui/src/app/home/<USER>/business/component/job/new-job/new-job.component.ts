import {Component, OnInit} from '@angular/core';
import {Job} from '../../../model/job';
import {JobService} from '../../../service/job.service';
import {MachineService} from '../../../service/machine.service';
import {Machine} from '../../../model/machine';
import {Employee} from '../../../../hr/model/employee';
import {EmployeeService} from '../../../../hr/service/employee.service';
import {NgForm} from '@angular/forms';
import {NotificationService} from '../../../../../core/service/notification.service';
import {SequenceService} from '../../../../../core/service/sequence.service';
import {Customer} from '../../../../trade/model/customer';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {NewCustomerComponent} from '../../../../trade/component/customer/new-customer/new-customer.component';
import {NewMachineComponent} from '../../machine/new-machine/new-machine.component';
import {CustomerReceiptComponent} from "../customer-receipt/customer-receipt.component";
import {ShowRoom} from "../../../model/show-room";
import {ShowRoomService} from "../../../service/showRoom.service";
import {CustomerService} from "../../../../trade/service/customer.service";
import {Warehouse} from "../../../../inventory/model/warehouse";
import {WarehouseService} from "../../../../inventory/service/warehouse.service";
import {ManageCustomerComponent} from "../../../../trade/component/customer/manage-customer/manage-customer.component";

@Component({
  standalone: false,
  selector: 'app-new-job',
  templateUrl: './new-job.component.html',
  styleUrls: ['./new-job.component.css']
})
export class NewJobComponent implements OnInit {

  job: Job;

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];

  keyMachineSearch: string;
  machineSearchList: Array<Machine> = [];

  keyEmpSearch: string;
  empSearchList: Array<Employee> = [];

  warehouses: Array<Warehouse> = [];

  showRooms: Array<ShowRoom>;

  modalRef: BsModalRef;

  constructor(private jobService: JobService, private customerService: CustomerService,
              private machineService: MachineService, private showRoomService: ShowRoomService,
              private employeeService: EmployeeService, private notificationService: NotificationService,
              private sequenceService: SequenceService, private modalService: BsModalService,
              private warehouseService: WarehouseService) {
  }

  ngOnInit() {
    this.job = new Job();
    this.setCurrentDate();
    this.job.machine = new Machine();
    this.job.customer = new Customer();
    this.job.showRoom = new ShowRoom();
    this.job.warehouse = new Warehouse();
    // Ensure warehouse id is null/undefined to prevent auto-selection
    this.job.warehouse.id = null;
    this.job.assignments = [];
    this.loadWarehouses();
    this.findAllShowrooms();
  }

  setCurrentDate() {
    this.job.jobDate = new Date();
  }

  searchCustomer() {
    this.modalRef = this.modalService.show(ManageCustomerComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.disableSetCustomer = false;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(event => {
      this.job.customer.id = this.modalRef.content.customer.id;
      this.keyCustomerSearch = this.job.customer.nicBr;
    })
  }

  loadWarehouses() {
    this.warehouseService.findAll().subscribe((result: Array<Warehouse>) => {
      return this.warehouses = result;
    })
  }

  searchEmployee() {
    this.employeeService.findByEmployeeNameLike(this.keyEmpSearch).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
    })
  }

  setSelectedEmp(event) {
    let employee = new Employee();
    employee.id = event.item.id;
    employee.name = event.item.name;
    this.job.assignments.push(employee);
  }

  findAllShowrooms() {
    this.showRoomService.findAll().subscribe((result: Array<ShowRoom>) => {
      return this.showRooms = result;
    })
  }

  searchMachines() {
    this.machineService.searchBySerialNoLike(this.keyMachineSearch).subscribe((result: Array<Machine>) => {
      return this.machineSearchList = result;
    })
  }

  setSelectedCustomer(event) {
    this.job.customer.id = event.item.id;
  }

  loadCustomerByNic() {
    if (this.keyCustomerSearch !== '') {
      this.customerService.findByNicLike(this.keyCustomerSearch).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
      });
    } else {
      this.ngOnInit();
    }
  }

  setSelectedMachine(event) {
    this.job.machine.id = event.item.id;
  }

  save(form: NgForm) {
    if (this.job.customer.id !== null && this.job.machine.id !== null) {
      this.jobService.save(this.job).subscribe(result => {
        if (result.code === 200) {
          this.notificationService.showSuccess(result.message);
          form.resetForm();
          this.showCustomerReceipt(result.data);
          this.ngOnInit();
        } else {
          this.notificationService.showError(result.message);
          console.log(result.data);
        }
      })
    }
  }

  clear() {
    this.ngOnInit();
    this.keyCustomerSearch = '';
    this.keyMachineSearch = '';
    this.keyEmpSearch = '';
  }

  showCustomerReceipt(jobId) {
    this.modalRef = this.modalService.show(CustomerReceiptComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.jobId = jobId;
    this.modalRef.content.initiate();
  }

  newCustomer() {
    this.modalRef = this.modalService.show(NewCustomerComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.isModal = true;
    this.modalRef.content.modalRef = this.modalRef;
  }

  newMachine() {
    this.modalRef = this.modalService.show(NewMachineComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRef.content.isModal = true;
    this.modalRef.content.modalRef = this.modalRef;
  }

}
