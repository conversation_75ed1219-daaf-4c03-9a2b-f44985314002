<div class="container-fluid">
  <div class="card shadow-sm">
    <div class="card-header bg-success text-white">
      <h4 class="mb-0">
        <i class="fa fa-check-circle me-2"></i>Completed Jobs
      </h4>
    </div>
    <div class="card-body">
      <!-- Search Section -->
      <div class="card mb-4">
        <div class="card-header bg-info text-white">
          <h6 class="mb-0">
            <i class="fa fa-search me-2"></i>Search Completed Jobs
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3 align-items-end">
            <div class="col-md-6">
              <label class="form-label fw-medium">
                <i class="fa fa-hashtag me-1"></i>Job Number
              </label>
              <input [(ngModel)]="keyJobNo" class="form-control" name="jobNo"
                     placeholder="Enter job number to search">
            </div>
            <div class="col-md-6">
              <div class="d-flex gap-2">
                <button (click)="searchJob()" class="btn btn-success">
                  <i class="fa fa-search me-2"></i>Search
                </button>
                <button (click)="findAllCompletedJobs()" class="btn btn-outline-primary">
                  <i class="fa fa-refresh me-2"></i>Reset
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Jobs Table -->
      <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
          <h6 class="mb-0">
            <i class="fa fa-table me-2"></i>Completed Jobs List
          </h6>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover table-striped mb-0">
              <thead class="table-dark">
                <tr>
                  <th class="fw-bold">
                    <i class="fa fa-hashtag me-1"></i>Job No
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-user me-1"></i>Customer Name
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-barcode me-1"></i>Serial No
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-tag me-1"></i>Category
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-info-circle me-1"></i>Status
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-calendar me-1"></i>Created Date
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let job of jobs,let i = index"
                    (click)="selectJob(job,i)"
                    [class.table-active]="i === selectedRow"
                    class="cursor-pointer">
                  <td class="fw-medium">{{job.jobNo}}</td>
                  <td>{{job.customer.name}}</td>
                  <td>{{job.machine.serialNo}}</td>
                  <td>{{job.machine.machineCategory.name}}</td>
                  <td>
                    <span class="badge bg-success">{{job.jobStatus.value}}</span>
                  </td>
                  <td>{{job.jobDate | date:'short'}}</td>
                </tr>
                <tr *ngIf="jobs.length === 0">
                  <td colspan="6" class="text-center text-muted py-4">
                    <i class="fa fa-inbox fa-2x mb-2 d-block"></i>
                    No completed jobs found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    <div class="d-flex">
      <div class="justify-content-center">
        <pagination class="pagination-sm justify-content-center mt-2"
          [totalItems]="collectionSize"
          [(ngModel)]="page"
          (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-end ms-2" (click)="invoice()">Invoice</button>
      </div>
    </div>
  </div>
</div>
