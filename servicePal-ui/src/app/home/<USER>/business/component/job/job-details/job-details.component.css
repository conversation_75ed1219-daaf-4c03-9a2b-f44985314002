/* Card Styling */
.card {
  border: none;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  border-radius: 12px;
}

.card-header {
  border-radius: 12px 12px 0 0 !important;
  border: none;
  padding: 1rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
}

/* Information Display Boxes */
.bg-light {
  background-color: #f8f9fa !important;
  border: 1px solid #e9ecef;
}

.bg-light:hover {
  background-color: #e9ecef !important;
  transition: background-color 0.2s ease;
}

/* Badge Styling */
.badge {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

.badge.fs-6 {
  font-size: 1rem !important;
  padding: 0.75rem 1rem;
}

/* Text Colors for Different Sections */
.text-primary {
  color: #0d6efd !important;
}

.text-success {
  color: #198754 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-secondary {
  color: #6c757d !important;
}

.text-danger {
  color: #dc3545 !important;
}

/* Form Labels */
.form-label {
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-label i {
  opacity: 0.8;
}

/* Button Styling */
.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
}

/* Animation for cards */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: slideIn 0.4s ease-out;
}

/* Staggered animation for multiple cards */
.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }
.card:nth-child(5) { animation-delay: 0.5s; }
.card:nth-child(6) { animation-delay: 0.6s; }

/* Close Button Styling */
.btn-close {
  font-size: 1.2rem;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.btn-close:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Custom Extra Wide Modal */
:host ::ng-deep .modal-xxl {
  max-width: 95vw !important;
  width: 95vw !important;
}

:host ::ng-deep .modal-xxl .modal-content {
  height: 90vh;
  overflow-y: auto;
}

/* Ensure modal is centered */
:host ::ng-deep .modal-dialog.modal-xxl {
  margin: 2.5vh auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .card-header {
    padding: 0.75rem 1rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }
}

/* Special styling for financial information */
.text-success.fs-5 {
  font-size: 1.25rem !important;
  font-weight: 700;
}

/* Hover effects for interactive elements */
.p-2.bg-light.rounded.border:hover {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Icon styling */
.fa {
  width: 1.2em;
  text-align: center;
}

/* Custom spacing */
.mb-4 {
  margin-bottom: 1.5rem !important;
}

.g-3 {
  gap: 1rem !important;
}
