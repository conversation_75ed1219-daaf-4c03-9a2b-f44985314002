<div class="container-fluid">
  <div class="card shadow-sm">
    <div class="card-header bg-primary text-white">
      <h4 class="mb-0">
        <i class="fa fa-tasks me-2"></i>Manage Jobs
      </h4>
    </div>
    <div class="card-body">
      <!-- Search Section -->
      <div class="card mb-4">
        <div class="card-header bg-info text-white">
          <h6 class="mb-0">
            <i class="fa fa-search me-2"></i>Search Jobs
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3 align-items-end">
            <div class="col-md-2">
              <label class="form-label fw-medium">
                <i class="fa fa-hashtag me-1"></i>Job Number
              </label>
              <input [(ngModel)]="keyJobNo" class="form-control" name="jobNo"
                     placeholder="Enter job number">
            </div>
            <div class="col-md-3">
              <label class="form-label fw-medium">
                <i class="fa fa-file-text me-1"></i>Work Order
              </label>
              <div class="input-group">
                <input [(ngModel)]="keyWoNo"
                       [typeahead]="jobListForWo"
                       (typeaheadLoading)="searchWo()"
                       [typeaheadOptionsLimit]="7"
                       typeaheadOptionField="woNumber"
                       placeholder="Search work order"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="searchWo">
                <button (click)="searchJob()" class="btn btn-outline-success">
                  <i class="fa fa-search"></i>
                </button>
              </div>
            </div>
            <div class="col-md-3">
              <label class="form-label fw-medium">
                <i class="fa fa-user me-1"></i>Customer
              </label>
              <div class="input-group">
                <input [(ngModel)]="keyCustomerSearch"
                       [typeahead]="customerSearchList"
                       (typeaheadLoading)="searchCustomers()"
                       (typeaheadOnSelect)="setSelectedCustomer($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadOptionField="name"
                       placeholder="Search customers"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="searchCustomer">
                <button class="btn btn-outline-success" (click)="searchCustomer()" type="button">
                  <i class="fa fa-search"></i>
                </button>
              </div>
            </div>
            <div class="col-md-3">
              <label class="form-label fw-medium">
                <i class="fa fa-cog me-1"></i>Machine Serial
              </label>
              <input [(ngModel)]="keyMachineSearch"
                     [typeahead]="machineSearchList"
                     (typeaheadLoading)="searchMachines()"
                     (typeaheadOnSelect)="setSelectedMachine($event)"
                     [typeaheadOptionsLimit]="7"
                     typeaheadOptionField="serialNo"
                     placeholder="Search by serial number"
                     autocomplete="off"
                     size="16"
                     class="form-control" name="searchCategory">
            </div>
            <div class="col-md-1">
              <button (click)="findAllPendingJobs()" class="btn btn-outline-primary">
                <i class="fa fa-refresh me-1"></i>Reset
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Jobs Table -->
      <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
          <h6 class="mb-0">
            <i class="fa fa-table me-2"></i>Jobs List
          </h6>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover table-striped mb-0">
              <thead class="table-dark">
                <tr>
                  <th class="fw-bold">
                    <i class="fa fa-hashtag me-1"></i>Job No
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-file-text me-1"></i>W.O No
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-user me-1"></i>Customer Name
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-barcode me-1"></i>Serial No
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-tag me-1"></i>Category
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-info-circle me-1"></i>Status
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-calendar me-1"></i>Created Date
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let job of jobs,let i = index"
                    (click)="selectJob(job,i)"
                    [class.table-active]="i === selectedRow"
                    class="cursor-pointer">
                  <td class="fw-medium">{{ job.jobNo }}</td>
                  <td>{{ null != job.woNumber ? job.woNumber : 'N/A' }}</td>
                  <td>{{ job.customer.name }}</td>
                  <td>{{ job.machine.serialNo }}</td>
                  <td>{{ job.machine.machineCategory.name }}</td>
                  <td>
                    <span class="badge bg-primary">{{ job.jobStatus.value }}</span>
                  </td>
                  <td>{{ job.jobDate | date:'short' }}</td>
                </tr>
                <tr *ngIf="jobs.length === 0">
                  <td colspan="7" class="text-center text-muted py-4">
                    <i class="fa fa-inbox fa-2x mb-2 d-block"></i>
                    No jobs found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="card-footer">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page" [maxSize]="15" [boundaryLinks]="true"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
      <!-- Action Buttons -->
      <div class="card">
        <div class="card-body">
          <div class="d-flex flex-wrap gap-2 justify-content-end">
            <button type="button" class="btn btn-outline-info" (click)="viewDetails()"
                    [disabled]="!selectedJob">
              <i class="fa fa-info-circle me-2"></i>Job Details
            </button>
            <button type="button" class="btn btn-outline-warning" (click)="showEstimate()"
                    [disabled]="!selectedJob">
              <i class="fa fa-calculator me-2"></i>Show Estimate
            </button>
            <button type="button" class="btn btn-outline-success" (click)="showInvoice()"
                    [disabled]="!selectedJob">
              <i class="fa fa-file-invoice me-2"></i>Show Invoice
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

