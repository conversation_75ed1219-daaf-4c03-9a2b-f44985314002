/* Card Styling */
.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-radius: 8px;
}

.card-header {
  border-radius: 8px 8px 0 0 !important;
  border: none;
}

/* Table Styling */
.table-active {
  background-color: #0d6efd !important;
  color: white !important;
}

.table-active:hover {
  background-color: #0b5ed7 !important;
  color: white !important;
}

/* Cursor Pointer */
.cursor-pointer {
  cursor: pointer;
}

/* Form Controls */
.form-control:focus,
.form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Button Styling */
.btn {
  border-radius: 6px;
  font-weight: 500;
}

/* Animation for cards */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card {
  animation: fadeIn 0.3s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .d-flex.flex-wrap.gap-2 {
    justify-content: center !important;
  }
}
