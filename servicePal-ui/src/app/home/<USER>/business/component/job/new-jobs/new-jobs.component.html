<div class="container-fluid">
  <div class="card shadow-sm">
    <div class="card-header bg-primary text-white">
      <h4 class="mb-0">
        <i class="fa fa-list-alt me-2"></i>New Jobs Management
      </h4>
    </div>
    <div class="card-body">
      <!-- Search Section -->
      <div class="card mb-4">
        <div class="card-header bg-info text-white">
          <h6 class="mb-0">
            <i class="fa fa-search me-2"></i>Search Jobs
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3 align-items-end">
            <div class="col-md-6">
              <label class="form-label fw-medium">
                <i class="fa fa-hashtag me-1"></i>Job Number
              </label>
              <input [(ngModel)]="keyJobNo" class="form-control" name="jobNo"
                     placeholder="Enter job number to search">
            </div>
            <div class="col-md-6">
              <div class="d-flex gap-2">
                <button (click)="searchJob()" class="btn btn-success">
                  <i class="fa fa-search me-2"></i>Search
                </button>
                <button (click)="findNewJobs()" class="btn btn-outline-primary">
                  <i class="fa fa-refresh me-2"></i>Reset
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Jobs Table -->
      <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
          <h6 class="mb-0">
            <i class="fa fa-table me-2"></i>Jobs List
          </h6>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover table-striped mb-0">
              <thead class="table-dark">
                <tr>
                  <th class="fw-bold">
                    <i class="fa fa-hashtag me-1"></i>Job No
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-user me-1"></i>Customer Name
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-barcode me-1"></i>Serial No
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-tag me-1"></i>Category
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-info-circle me-1"></i>Status
                  </th>
                  <th class="fw-bold">
                    <i class="fa fa-calendar me-1"></i>Created Date
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let job of jobs,let i = index"
                    (click)="selectJob(job,i)"
                    [class.table-active]="i === selectedRow"
                    class="cursor-pointer">
                  <td class="fw-medium">{{job.jobNo}}</td>
                  <td>{{job.customer.name}}</td>
                  <td>{{job.machine.serialNo}}</td>
                  <td>{{job.machine.machineCategory.name}}</td>
                  <td>
                    <span class="badge bg-info">{{job.jobStatus.value}}</span>
                  </td>
                  <td>{{job.jobDate | date:'short'}}</td>
                </tr>
                <tr *ngIf="jobs.length === 0">
                  <td colspan="6" class="text-center text-muted py-4">
                    <i class="fa fa-inbox fa-2x mb-2 d-block"></i>
                    No jobs found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="card-footer">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
              [totalItems]="collectionSize"
              [(ngModel)]="page"
              (pageChanged)="pageChanged($event)"
              [maxSize]="10">
            </pagination>
          </div>
        </div>
      </div>
      <!-- Action Buttons -->
      <div class="card">
        <div class="card-body">
          <div class="d-flex flex-wrap gap-2 justify-content-end">
            <button type="button" class="btn btn-outline-info" (click)="showCustomerReceipt()"
                    [disabled]="!selectedJob.id">
              <i class="fa fa-receipt me-2"></i>Customer Receipt
            </button>
            <button type="button" class="btn btn-outline-secondary" (click)="showJobCard()"
                    [disabled]="!selectedJob.id">
              <i class="fa fa-id-card me-2"></i>Job Card
            </button>
            <button type="button" class="btn btn-success" (click)="createEstimate()"
                    [disabled]="!selectedJob.id">
              <i class="fa fa-calculator me-2"></i>Create/Edit Estimate
            </button>
            <button type="button" class="btn btn-warning"
                    [disabled]="!selectedJob.id || (selectedJob.jobStatus && selectedJob.jobStatus.value !== 'Job Estimated')"
                    (click)="openModalApproveEstimate(approveEstimateModal)">
              <i class="fa fa-check me-2"></i>Approve Estimate
            </button>
            <button type="button" class="btn btn-primary" (click)="makePayment()"
                    [disabled]="!selectedJob.id">
              <i class="fa fa-credit-card me-2"></i>Make Payment
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #approveEstimateModal>
  <div class="modal-body row">
    <div class="col-md-6">
      <p><b>Job No : </b>{{selectedJob.jobNo}}</p>
      <p><b>Customer : </b>{{selectedJob.customer.name}}</p>
      <p><b>Serial : </b>{{selectedJob.machine.serialNo}}</p>
    </div>
    <div class="col-md-6">
      <label>Approved Date</label>
      <input #approvedDt="ngModel" type="text" name="approvedDt" id="approvedDt"
             [(ngModel)]="approvedDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
             [minDate]="toDay" class="form-control" placeholder="Enter Job Approved Date">
      <button class="btn btn-success float-end mt-4" mwlConfirmationPopover (confirm)="approveEstimate()">Approve
        Estimate
      </button>
    </div>
  </div>
</ng-template>


