<div class="container-fluid">
  <div class="card shadow-sm">
    <div class="card-header bg-primary text-white">
      <h4 class="mb-0">
        <i class="fa fa-plus-circle me-2"></i>Create New Job
      </h4>
    </div>
    <div class="card-body p-4">
      <form #newJobForm="ngForm" (ngSubmit)="save(newJobForm);">

        <!-- Basic Information Section -->
        <div class="card mb-4">
          <div class="card-header bg-info text-white">
            <h6 class="mb-0">
              <i class="fa fa-info-circle me-2"></i>Basic Information
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-4">
                <label class="form-label fw-medium">
                  <i class="fa fa-calendar me-1"></i>Job Date <span class="text-danger">*</span>
                </label>
                <input required #jobDate="ngModel" type="text" name="jobDate" id="jobDate"
                       [(ngModel)]="job.jobDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                       [class.is-invalid]="jobDate.invalid && jobDate.touched"
                       class="form-control" placeholder="Select job date">
                <div class="invalid-feedback">
                  Job Date is required
                </div>
              </div>

              <div class="col-md-4">
                <label class="form-label fw-medium">
                  <i class="fa fa-file-text me-1"></i>Work Order No
                </label>
                <input type="text" #woNo="ngModel" class="form-control" id="woNo"
                       name="woNo" placeholder="Enter work order number"
                       [(ngModel)]="job.woNumber">
              </div>

              <div class="col-md-4">
                <label class="form-label fw-medium">
                  <i class="fa fa-warehouse me-1"></i>Spare Parts Location <span class="text-danger">*</span>
                </label>
                <select class="form-select" required #selectedWh="ngModel"
                        [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                        [(ngModel)]="job.warehouse.id">
                  <option value="">Select spare parts location</option>
                  <option *ngFor="let warehouse of warehouses" [value]="warehouse.id">{{warehouse.name}}</option>
                </select>
                <div class="invalid-feedback">
                  Please select a spare parts location
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Customer and Machine Section -->
        <div class="card mb-4">
          <div class="card-header bg-success text-white">
            <h6 class="mb-0">
              <i class="fa fa-users me-2"></i>Customer & Machine Details
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-user me-1"></i>Customer <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                  <input [(ngModel)]="keyCustomerSearch"
                         [typeahead]="customerSearchList"
                         (typeaheadLoading)="loadCustomerByNic()"
                         (typeaheadOnSelect)="setSelectedCustomer($event)"
                         [typeaheadOptionsLimit]="7"
                         typeaheadOptionField="nicBr"
                         placeholder="Search customers by NIC/BR"
                         autocomplete="off"
                         size="16"
                         class="form-control" name="searchCustomer">
                  <button class="btn btn-outline-primary" (click)="newCustomer()"
                          type="button" title="Add New Customer">
                    <i class="fa fa-plus"></i>
                  </button>
                  <button class="btn btn-outline-success" (click)="searchCustomer()"
                          type="button" title="Search Customer">
                    <i class="fa fa-search"></i>
                  </button>
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-cog me-1"></i>Machine Serial <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                  <input [(ngModel)]="keyMachineSearch"
                         [typeahead]="machineSearchList"
                         (typeaheadLoading)="searchMachines()"
                         (typeaheadOnSelect)="setSelectedMachine($event)"
                         [typeaheadOptionsLimit]="7"
                         typeaheadOptionField="serialNo"
                         placeholder="Search by serial number"
                         autocomplete="off"
                         size="16"
                         class="form-control" name="searchCategory">
                  <button class="btn btn-outline-primary" (click)="newMachine()"
                          type="button" title="Add New Machine">
                    <i class="fa fa-plus"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Technician and Job Details Section -->
        <div class="card mb-4">
          <div class="card-header bg-warning text-dark">
            <h6 class="mb-0">
              <i class="fa fa-wrench me-2"></i>Technician & Job Details
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-user-cog me-1"></i>Technician Name
                </label>
                <input [(ngModel)]="keyEmpSearch"
                       [typeahead]="empSearchList"
                       (typeaheadLoading)="searchEmployee()"
                       (typeaheadOnSelect)="setSelectedEmp($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadOptionField="name"
                       placeholder="Search technician by name"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="searchEmp">
                <div class="mt-2">
                  <tag-input [ngModel]="job.assignments" [identifyBy]="'id'" [displayBy]="'name'"
                             [hideForm]="true" name="techList"></tag-input>
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-exclamation-triangle me-1"></i>Damages
                </label>
                <input type="text" #damages="ngModel" class="form-control" id="damages"
                       name="remark" placeholder="Describe any damages"
                       [class.is-invalid]="damages.invalid && damages.touched"
                       [(ngModel)]="job.damages">
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Information Section -->
        <div class="card mb-4">
          <div class="card-header bg-secondary text-white">
            <h6 class="mb-0">
              <i class="fa fa-clipboard-list me-2"></i>Additional Information
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-minus-circle me-1"></i>Missing Parts
                </label>
                <input type="text" #lessParts="ngModel" class="form-control" id="lessParts"
                       name="lessParts" placeholder="List any missing parts"
                       [class.is-invalid]="lessParts.invalid && lessParts.touched"
                       [(ngModel)]="job.lessParts">
              </div>

              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-bug me-1"></i>Defects
                </label>
                <input type="text" #defects="ngModel" class="form-control" id="defect"
                       name="defect" placeholder="Describe defects found"
                       [class.is-invalid]="defects.invalid && defects.touched"
                       [(ngModel)]="job.defect">
              </div>

              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-store me-1"></i>Showroom <span class="text-danger">*</span>
                </label>
                <select required #showroom="ngModel"
                        [class.is-invalid]="showroom.invalid && showroom.touched"
                        class="form-select" id="showroom" [(ngModel)]="job.showRoom.id" name="showroom">
                  <option value="">Select showroom</option>
                  <option *ngFor="let sr of showRooms" [value]="sr.id">{{sr.name}}</option>
                </select>
                <div class="invalid-feedback">
                  Please select a showroom
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-money-bill me-1"></i>Advance Payment
                </label>
                <div class="input-group">
                  <span class="input-group-text">$</span>
                  <input type="number" #advance="ngModel" class="form-control" id="advance"
                         name="advance" placeholder="0.00" step="0.01"
                         [class.is-invalid]="advance.invalid && advance.touched"
                         [(ngModel)]="job.advancePayment">
                </div>
              </div>

              <div class="col-md-12">
                <label class="form-label fw-medium">
                  <i class="fa fa-comment me-1"></i>Remarks
                </label>
                <textarea #remark="ngModel" class="form-control" id="remark" rows="3"
                          name="remark" placeholder="Enter any additional remarks or notes"
                          [class.is-invalid]="remark.invalid && remark.touched"
                          [(ngModel)]="job.remark"></textarea>
              </div>
            </div>
          </div>
        </div>
        <!-- Action Buttons -->
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <small class="text-muted">
                  <i class="fa fa-info-circle me-1"></i>
                  Fields marked with <span class="text-danger">*</span> are required
                </small>
              </div>
              <div class="d-flex gap-3">
                <button type="button" class="btn btn-outline-secondary btn-lg px-4" (click)="clear()">
                  <i class="fa fa-eraser me-2"></i>Clear Form
                </button>
                <button type="submit" class="btn btn-success btn-lg px-4"
                        [disabled]="!newJobForm.form.valid">
                  <i class="fa fa-save me-2"></i>Create Job
                  <span class="spinner-border spinner-border-sm ms-2" *ngIf="newJobForm.pending"></span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
