import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {HeaderComponent} from './layout/header/header.component';
import {FooterComponent} from './layout/footer/footer.component';
import {PermissionsSidebarComponent} from './layout/permissions-sidebar/permissions-sidebar.component';
import {SideBarComponent} from './layout/side-bar/side-bar.component';
import {AllPermissionsComponent} from './component/all-permissions/all-permissions.component';
import {UserPermissionsComponent} from './component/user-permissions/user-permissions.component';
import {DesktopManagerComponent} from './component/desktop-manager/desktop-manager.component';
import {StarterComponent} from './starter/starter.component';

const routes: Routes = [];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class CoreRoutingModule {
}

export const routeParams = [HeaderComponent, FooterComponent, SideBarComponent,
  PermissionsSidebarComponent, AllPermissionsComponent, UserPermissionsComponent, DesktopManagerComponent,
  StarterComponent];
