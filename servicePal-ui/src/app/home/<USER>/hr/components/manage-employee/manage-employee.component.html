<div class="card">
  <div class="card-header">
    <strong>Manage Employee</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="input-group col-md-6 mb-2">
        <input [(ngModel)]="keyEmployee"
               [typeahead]="employees"
               (typeaheadLoading)="loadEmployees()"
               (typeaheadOnSelect)="setSelectedEmployee($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="name"
               placeholder="Search Employee"
               autocomplete="off"
               size="16"
               class="form-control" name="brand">
      </div>
    </div>

    <table class="table table-bordered table-striped table-sm">
      <thead>
      <tr style="text-align: center">
        <th scope="col">Employee Name</th>
        <th scope="col">NIC</th>
        <th scope="col">Contact Number</th>
        <th scope="col">Designation</th>
        <th scope="col">Salary Scale</th>
        <th scope="col">Reporting Manager</th>
      </tr>
      </thead>
      <tbody>
      <tr style="text-align: center" *ngFor="let employee of employees,let i = index"
          (click)="employeeDetail(employee,i)"
          [class.active]="i === selectedRow">
        <td>{{employee.name}}</td>
        <td>{{employee.nic}}</td>
        <td>{{employee.telephone1}}</td>

        <td>{{employee.designation.designationName}}</td>
        <td>{{employee.salaryScale.name}}</td>
        <td>{{employee.reportingManager}}</td>

      </tr>
      </tbody>
    </table>

    <div class="row float-end">
      <div class="me-3">
        <button class="btn btn-success " type="button" (click)="openModal(true)"
                [disabled]="selectedRow===null">View
        </button>

      </div>
      <div class="me-3">
        <button class="btn btn-success " type="button" (click)="openModal(false)"
                [disabled]="selectedRow===null ">Edit
        </button>
      </div>
    </div>
    <pagination class="pagination-sm justify-content-center mt-2"
      [totalItems]="collectionSize"
      [(ngModel)]="page"
      (pageChanged)="pageChanged($event)">
    </pagination>
  </div>
</div>

<ng-template #employeeUpdateModal>

  <div class="modal-header">
    <button type="button" class="close float-end" aria-label="Close" (click)="modalRef.hide();ngOnInit();">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-employee></app-employee>
  </div>

</ng-template>

