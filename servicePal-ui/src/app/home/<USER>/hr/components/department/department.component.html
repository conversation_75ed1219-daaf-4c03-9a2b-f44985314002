<div class="card">
  <div class="card-header">
    <strong>MANAGE DEPARTMENT</strong></div>
  <div class="card-body">

    <div class="row">
      <div class="col-md-6">
        <table class="table table-bordered table-striped table-sm">
          <thead>
          <tr>
            <th>Department</th>

          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let dep of departments,let i = index "(click)="departmentDetail(dep);setClickedRow(i);"
              [class.active]="i === selectedRow">
            <td>{{dep.departmentName}}</td>
          </tr>
          </tbody>
        </table>
        <div class="row">
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center mt-2"
              [totalItems]="collectionSize"
              [(ngModel)]="page"
              (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <form #ManageDepartment="ngForm">
          <label>Department </label>
          <input type="text" required #design="ngModel" [class.is-invalid]="design.invalid && design.touched"
                 class="form-control" id="bName" (keyup)="checkValidDepartment()"  [(ngModel)]="department.departmentName" name="department"
                 placeholder="Enter Department ">
          <div *ngIf="design.errors && (design.invalid || design.touched)">
            <small class="text-danger" [class.d-none]="design.valid || design.untouched">*department is required
            </small>
          </div>
          <small *ngIf="invalidDepartment" [class.is-none]="true" class="text-danger">* Department is
            already used
          </small>
          <div class="row ms-1">
            <div class="form-check me-5 col-md-6">
              <input type="checkbox" class="form-check-input" id="check4" name="check4" value=""
                     [(ngModel)]="department.active">
              <label for="check4" class="form-check-label">Is Active</label>
            </div>
          </div>
          <div class="row float-end mt-2">
            <div class="me-3">
              <button class="btn btn-success" [disabled]="!ManageDepartment.form.valid || invalidDepartment"
                      (confirm)="save(ManageDepartment)" mwlConfirmationPopover>save</button>
            </div>
            <div class="me-3">
              <button class="btn btn-warning"  (click)="Clear()">Clear</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
