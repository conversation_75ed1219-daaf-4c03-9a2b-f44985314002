<div class="card">
  <div class="card-header">
    <strong>Manage Leaves</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-4">
        <div class="mb-3 ">
          <input [(ngModel)]="keyEpf"
                 [typeahead]="leaves"
                 (typeaheadLoading)="loadEpf()"
                 (typeaheadOnSelect)="setSelectedEpf($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="epf"
                 placeholder="Search By Epf No"
                 autocomplete="off"
                 size="16"
                 required
                 class="form-control" name="epf">
        </div>
      </div>
    </div>
    <table class="table table-bordered table-striped table-sm">
      <thead>
      <tr style="text-align: center">
        <th scope="col">Employee Name</th>
        <th scope="col">Epf No</th>
        <th scope="col">From</th>
        <th scope="col">To</th>
        <th scope="col">Action</th>
      </tr>
      </thead>
      <tbody>
      <tr style="text-align: center" *ngFor="let leave of leaves,let i = index"
          (click)="leaveRequestDetail(leave,i)"
          [class.active]="i === selectedRow">
        <td>{{leave.employee.name}}</td>
        <td>{{leave.employee.epfNo}}</td>
        <td>{{leave.from | date: '+530'}}</td>
        <td>{{leave.to | date : '+530'}}</td>
        <td>
          <button (click)="confirm()">confirm</button>
          <button (click)="notConfirm()">not confirm</button>
        </td>
      </tr>
      </tbody>
    </table>

    <div class="row float-end">
      <div class="me-3">
        <button class="btn btn-success " type="button" (click)="openModal(template)">View More</button>
      </div>
      <!--<div class="me-3">-->
      <!--<button class="btn btn-success " type="button" (click)="openModal(customerUpdateModal)"-->
      <!--[disabled]="selectedRow==null">Edit-->
      <!--</button>-->
      <!--</div>-->
    </div>
    <pagination class="pagination-sm justify-content-center mt-2"
      [totalItems]="collectionSize"
      [(ngModel)]="page"
      (pageChanged)="pageChanged($event)">
    </pagination>
  </div>
</div>

<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title float-start"></h4>
    <button type="button" class="close float-end" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="card">
      <div class="card-header">
        <strong>View </strong>
        <small>Details</small>
      </div>
      <div class="card-body">
        <table class="table table-bordered table-striped table-sm">
          <thead>
          <tr style="text-align: center">
            <th scope="col">Leave Type</th>
            <th scope="col">Reason</th>
            <th scope="col">Covering Employee</th>
          </tr>
          </thead>
          <tbody>
          <tr style="text-align: center" *ngFor="let le of leaves,let i = index">
            <td>{{le.type.leaveType}}</td>
            <td>{{le.reason}}</td>
            <td>{{le.coveringEmp.firstName + ' ' + le.coveringEmp.lastName}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</ng-template>


