<div class="min-vh-100 d-flex align-items-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-4 col-md-6 col-sm-8">
        <div class="card shadow-lg border-0" style="border-radius: 15px;">
          <div class="card-body p-5">
            <!-- Logo Section -->
            <div class="text-center mb-4">
              <div class="mb-3">
                <img src="img/brand/company-logo.png" class="img-fluid" width="80" height="80"
                     style="border-radius: 50%; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
              </div>
              <h2 class="fw-bold text-primary mb-1">ServicePal</h2>
              <p class="text-muted mb-0">Professional Service Management</p>
            </div>

            <!-- Login Form -->
            <form #loginForm="ngForm" (ngSubmit)="login()">
              <div class="text-center mb-4">
                <h5 class="text-dark">Welcome Back!</h5>
                <p class="text-muted small">Please sign in to your account</p>
              </div>

              <!-- Username Field -->
              <div class="mb-3">
                <label class="form-label fw-medium">
                  <i class="fa fa-user me-2 text-primary"></i>Username
                </label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="fa fa-user text-muted"></i>
                  </span>
                  <input type="text" class="form-control border-start-0 ps-0"
                         placeholder="Enter your username"
                         autocomplete="username"
                         [(ngModel)]="username"
                         name="username"
                         required
                         #usernameField="ngModel"
                         [class.is-invalid]="usernameField.invalid && usernameField.touched">
                </div>
                <div class="invalid-feedback" *ngIf="usernameField.invalid && usernameField.touched">
                  Username is required
                </div>
              </div>

              <!-- Password Field -->
              <div class="mb-4">
                <label class="form-label fw-medium">
                  <i class="fa fa-lock me-2 text-primary"></i>Password
                </label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="fa fa-lock text-muted"></i>
                  </span>
                  <input type="password" class="form-control border-start-0 ps-0"
                         placeholder="Enter your password"
                         autocomplete="current-password"
                         [(ngModel)]="password"
                         required
                         name="password"
                         #passwordField="ngModel"
                         [class.is-invalid]="passwordField.invalid && passwordField.touched">
                </div>
                <div class="invalid-feedback" *ngIf="passwordField.invalid && passwordField.touched">
                  Password is required
                </div>
              </div>

              <!-- Login Button -->
              <div class="d-grid mb-3">
                <button type="submit"
                        class="btn btn-primary btn-lg fw-medium"
                        [disabled]="!loginForm.form.valid || loading"
                        style="border-radius: 10px;">
                  <span *ngIf="loading" class="spinner-border spinner-border-sm me-2"></span>
                  <i class="fa fa-sign-in-alt me-2" *ngIf="!loading"></i>
                  {{ loading ? 'Signing In...' : 'Sign In' }}
                </button>
              </div>

              <!-- Error Message -->
              <div class="alert alert-danger" *ngIf="error && submitted" role="alert">
                <i class="fa fa-exclamation-triangle me-2"></i>
                {{ error.message || 'Login failed. Please try again.' }}
              </div>
            </form>
          </div>

          <!-- Footer -->
          <div class="card-footer bg-light text-center border-0" style="border-radius: 0 0 15px 15px;">
            <small class="text-muted">
              <i class="fa fa-code me-1"></i>
              Powered by <strong class="text-primary">S-OUT Solutions Pvt. Ltd</strong>
            </small>
          </div>
        </div>

        <!-- Additional Info -->
        <div class="text-center mt-4">
          <p class="text-white small mb-0">
            <i class="fa fa-shield-alt me-1"></i>
            Secure & Professional Service Management System
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
