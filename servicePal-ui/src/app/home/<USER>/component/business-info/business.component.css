/* Card Styling */
.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-radius: 8px;
}

.card-header {
  border-radius: 8px 8px 0 0 !important;
  border: none;
}

/* Form Controls */
.form-control:focus,
.form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Button Styling */
.btn {
  border-radius: 6px;
  font-weight: 500;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 1.1rem;
}

/* Required Field Indicator */
.text-danger {
  font-weight: 600;
}

/* Form Labels */
.form-label {
  margin-bottom: 0.5rem;
  color: #495057;
}

.form-label i {
  color: #6c757d;
}

/* Image Preview Styling */
.img-fluid {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Animation for cards */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card {
  animation: fadeIn 0.3s ease-in-out;
}

/* Validation Styling */
.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .btn-lg {
    padding: 10px 20px;
    font-size: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
}
