<div class="container-fluid">
  <div class="card shadow-sm">
    <div class="card-header bg-primary text-white">
      <h4 class="mb-0">
        <i class="fa fa-building me-2"></i>Business Information
      </h4>
    </div>
    <div class="card-body">
      <form #companyForm=ngForm (ngSubmit)="saveCompany();companyForm.reset()">

        <!-- Basic Company Information -->
        <div class="card mb-4">
          <div class="card-header bg-info text-white">
            <h6 class="mb-0">
              <i class="fa fa-info-circle me-2"></i>Company Details
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-building me-1"></i>Company Name <span class="text-danger">*</span>
                </label>
                <input required #company_name="ngModel" type="text" name="company_name" id="company_name"
                       [class.is-invalid]="company_name.invalid && company_name.touched" [(ngModel)]="company.name"
                       class="form-control" placeholder="Enter company name">
                <div class="invalid-feedback">
                  Company Name is required
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-quote-left me-1"></i>Company Slogan
                </label>
                <input type="text" name="company_slogan" id="company_slogan"
                       [(ngModel)]="company.slogan" class="form-control" placeholder="Enter company slogan">
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="card mb-4">
          <div class="card-header bg-success text-white">
            <h6 class="mb-0">
              <i class="fa fa-phone me-2"></i>Contact Information
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-4">
                <label class="form-label fw-medium">
                  <i class="fa fa-phone me-1"></i>Primary Contact <span class="text-danger">*</span>
                </label>
                <input required #company_contact_1="ngModel" type="text" name="company_contact_1" id="company_contact_1"
                       [class.is-invalid]="company_contact_1.invalid && company_contact_1.touched"
                       [(ngModel)]="company.telephone1" class="form-control" pattern="^\d{10}$"
                       placeholder="Enter primary contact">
                <div class="invalid-feedback">
                  Primary contact is required
                </div>
              </div>

              <div class="col-md-4">
                <label class="form-label fw-medium">
                  <i class="fa fa-phone me-1"></i>Secondary Contact
                </label>
                <input type="text" name="company_contact_2" #company_contact_2="ngModel"
                       id="company_contact_2"
                       [(ngModel)]="company.telephone2" class="form-control" pattern="^\d{10}$"
                       placeholder="Enter secondary contact">
              </div>

              <div class="col-md-4">
                <label class="form-label fw-medium">
                  <i class="fa fa-phone me-1"></i>Additional Contact
                </label>
                <input type="text" name="company_contact_3" #company_contact_3="ngModel"
                       id="company_contact_3"
                       [(ngModel)]="company.telephone3" class="form-control" pattern="^\d{10}$"
                       placeholder="Enter additional contact">
              </div>
            </div>
          </div>
        </div>

        <!-- Address and Legal Information -->
        <div class="card mb-4">
          <div class="card-header bg-warning text-dark">
            <h6 class="mb-0">
              <i class="fa fa-map-marker-alt me-2"></i>Address & Legal Information
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-12">
                <label class="form-label fw-medium">
                  <i class="fa fa-map-marker-alt me-1"></i>Company Address <span class="text-danger">*</span>
                </label>
                <textarea #address2="ngModel" class="form-control" id="address2" rows="3"
                          name="address2" placeholder="Enter complete company address"
                          [class.is-invalid]="address2.invalid && address2.touched"
                          [(ngModel)]="company.fullAddress" required></textarea>
                <div class="invalid-feedback">
                  Company address is required
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-envelope me-1"></i>Company Email <span class="text-danger">*</span>
                </label>
                <input #company_email="ngModel" type="email" name="company_email" id="company_email"
                       pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"
                       [class.is-invalid]="company_email.invalid && company_email.touched" [(ngModel)]="company.email"
                       class="form-control" placeholder="Enter company email" required>
                <div class="invalid-feedback">
                  Valid company email is required
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-certificate me-1"></i>Registration Number <span class="text-danger">*</span>
                </label>
                <input #regNo="ngModel" type="text" class="form-control" id="regNo"
                       name="regNo" placeholder="Enter registration number"
                       [class.is-invalid]="regNo.invalid && regNo.touched"
                       [(ngModel)]="company.regNo" required>
                <div class="invalid-feedback">
                  Registration number is required
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Company Logo -->
        <div class="card mb-4">
          <div class="card-header bg-secondary text-white">
            <h6 class="mb-0">
              <i class="fa fa-image me-2"></i>Company Logo
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-6">
                <label class="form-label fw-medium">
                  <i class="fa fa-upload me-1"></i>Upload Logo
                </label>
                <input class="form-control" type="file" #logo
                       (change)="getFiles($event)"
                       accept="image/*">
                <small class="form-text text-muted">
                  Recommended size: 250x250 pixels. Supported formats: JPG, PNG, GIF
                </small>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Preview</label>
                <div class="border rounded p-3 text-center bg-light">
                  <img [(src)]="imageFile"
                       class="img-fluid rounded shadow-sm"
                       style="max-width: 200px; max-height: 200px;"
                       *ngIf="imageFile; else noImage">
                  <ng-template #noImage>
                    <div class="text-muted">
                      <i class="fa fa-image fa-3x mb-2"></i>
                      <p>No logo uploaded</p>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <small class="text-muted">
                  <i class="fa fa-info-circle me-1"></i>
                  Fields marked with <span class="text-danger">*</span> are required
                </small>
              </div>
              <div>
                <button type="submit" [disabled]="!companyForm.form.valid"
                        class="btn btn-success btn-lg px-4">
                  <i class="fa fa-save me-2"></i>Save Business Information
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

