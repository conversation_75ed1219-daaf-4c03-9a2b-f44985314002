/* ===== THEME COLORS ===== */
.theme-color {
  color: #667eea;
}

.theme-color-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* ===== UTILITY CLASSES ===== */
.cursor-pointer {
  cursor: pointer;
}

.text-purple {
  color: #6f42c1 !important;
}

.bg-purple {
  background-color: #6f42c1 !important;
}

/* ===== CUSTOM MODAL SIZES ===== */
.modal-xxl {
  max-width: 95vw !important;
  width: 95vw !important;
}

.modal-xxl .modal-content {
  height: 90vh;
  overflow-y: auto;
}

.modal-dialog.modal-xxl {
  margin: 2.5vh auto;
}

/* ===== PROFESSIONAL CARD STYLING ===== */
.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.5rem 0.5rem 0 0 !important;
  padding: 1rem 1.25rem;
}

.card-header h5 {
  margin-bottom: 0;
  font-weight: 600;
}

/* ===== PROFESSIONAL FORM STYLING ===== */
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #495057;
}

.form-control, .form-select {
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.input-group-text {
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  color: #6c757d;
}

/* ===== PROFESSIONAL TABLE STYLING ===== */
.table {
  margin-bottom: 0;
}

.table thead th {
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

.table-dark thead th {
  background-color: #343a40;
  border-color: #454d55;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.025);
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

/* ===== PROFESSIONAL BUTTON STYLING ===== */
.btn {
  font-weight: 500;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  transition: all 0.15s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.btn-group .btn + .btn {
  margin-left: 0.5rem;
}

/* ===== BADGE STYLING ===== */
.badge {
  font-weight: 500;
  border-radius: 0.375rem;
  padding: 0.35em 0.65em;
}

/* ===== ALERT STYLING ===== */
.alert {
  border: none;
  border-radius: 0.5rem;
  padding: 1rem 1.25rem;
}

.alert-dismissible .btn-close {
  padding: 1.25rem 1rem;
}

::ng-deep .modal-dialog.modal-80 {
  max-width: 1000px !important;
}

/* ===== MODAL IMPROVEMENTS ===== */
::ng-deep .modal-dialog {
  margin: 1.75rem auto;
}

::ng-deep .modal-lg {
  max-width: 900px;
}

::ng-deep .modal-xl {
  max-width: 1200px;
}

/* Ensure modals have proper spacing */
::ng-deep .modal-body {
  padding: 1.5rem;
}

::ng-deep .modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #dee2e6;
}

::ng-deep .modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #dee2e6;
}

/* Better close button styling */
::ng-deep .btn-close {
  padding: 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
}

::ng-deep .btn-close-white {
  filter: invert(1) grayscale(100%) brightness(200%);
}

/* Table row highlighting - Enhanced for all components */
.table tr.active td,
.table tbody tr.active,
.table tbody tr.table-active,
table tr.active td,
table tbody tr.active,
table tbody tr.table-active {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border-left: 3px solid #2196f3 !important;
}

.table tbody tr.active:hover,
.table tbody tr.table-active:hover,
table tbody tr.active:hover,
table tbody tr.table-active:hover {
  background-color: #bbdefb !important;
  color: #1565c0 !important;
}

/* Table row hover effect */
.table tbody tr:hover,
table tbody tr:hover {
  background-color: #f5f5f5 !important;
  cursor: pointer;
}

/* Selected row styling for better visibility */
.table tbody tr.selected {
  background-color: #e8f5e8 !important;
  color: #2e7d32 !important;
  border-left: 4px solid #4caf50 !important;
}

.table tbody tr.selected:hover {
  background-color: #c8e6c9 !important;
  color: #1b5e20 !important;
}

/* Ensure table row highlighting works with striped tables */
.table-striped tbody tr.active,
.table-striped tbody tr.table-active,
table.table-striped tbody tr.active,
table.table-striped tbody tr.table-active {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border-left: 3px solid #2196f3 !important;
}

.table-striped tbody tr.active:hover,
.table-striped tbody tr.table-active:hover,
table.table-striped tbody tr.active:hover,
table.table-striped tbody tr.table-active:hover {
  background-color: #bbdefb !important;
  color: #1565c0 !important;
}

/* Professional table styling */
.table-professional tbody tr.active,
.table-professional tbody tr.table-active {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border-left: 3px solid #2196f3 !important;
}

.table-professional tbody tr.active:hover,
.table-professional tbody tr.table-active:hover {
  background-color: #bbdefb !important;
  color: #1565c0 !important;
}

/* Override any conflicting styles from other CSS files */
.table tr.active td,
table tr.active td {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border-left: 3px solid #2196f3 !important;
}

/* Ensure consistent highlighting across all table types */
.table tbody tr.active,
.table tbody tr.table-active,
table tbody tr.active,
table tbody tr.table-active,
.table-hover tbody tr.active,
.table-hover tbody tr.table-active,
.table-striped tbody tr.active,
.table-striped tbody tr.table-active,
.table-professional tbody tr.active,
.table-professional tbody tr.table-active {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border-left: 3px solid #2196f3 !important;
}
