{"name": "servicepal", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.15", "@angular/common": "^19.2.15", "@angular/compiler": "^19.2.15", "@angular/core": "^19.2.15", "@angular/forms": "^19.2.15", "@angular/platform-browser": "^19.2.15", "@angular/platform-browser-dynamic": "^19.2.15", "@angular/router": "^19.2.15", "@fortawesome/fontawesome-free": "^6.5.2", "angular-confirmation-popover": "^7.0.0", "bootstrap": "^5.3.3", "jsbarcode": "^3.11.6", "ngx-barcode6": "^1.0.25", "ngx-bootstrap": "19.0.2", "ngx-chips": "^3.0.0", "ngx-print": "^20.0.0", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.2", "tslib": "^2.8.1", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.15", "@angular/cli": "^19.2.15", "@angular/compiler-cli": "^19.2.15", "@types/jasmine": "~5.1.8", "jasmine-core": "~5.8.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.3"}}