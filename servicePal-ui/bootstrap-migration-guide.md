# Bootstrap 5 Migration Guide for ServicePal

## Overview
This guide provides standardized patterns for migrating components to use consistent Bootstrap 5 styling across the ServicePal application.

## Icon Standardization

### New Icon Service
Use the `IconService` for all icons:

```typescript
// Inject the service
constructor(private iconService: IconService) {}

// Get module icons
this.iconService.getModuleIcon('Inventory') // Returns: 'fas fa-boxes text-primary'

// Get permission icons
this.iconService.getPermissionIcon('Create Item') // Returns: 'fas fa-plus-circle text-success'

// Get action icons
this.iconService.getActionIcon('edit') // Returns: 'fas fa-edit text-warning'
```

### Updated Permission Icons (Backend)
All permission icons in `InitDataRunner.java` have been updated to use:
- FontAwesome 5+ classes (`fas` instead of `fa`)
- Consistent color schemes using Bootstrap color utilities
- Semantic icons that match the action/function

## Bootstrap Component Patterns

### 1. Card Headers
**Before:**
```html
<div class="card">
  <div class="card-header">
    <strong>TITLE</strong>
  </div>
```

**After:**
```html
<div class="card shadow-sm">
  <div class="card-header bg-primary text-white">
    <h5 class="mb-0">
      <i class="fas fa-icon me-2"></i>
      <strong>TITLE</strong>
    </h5>
  </div>
```

### 2. Form Fields
**Before:**
```html
<div class="mb-3 col-md-3">
  <label>Field Name</label>
  <input type="text" class="form-control" placeholder="placeholder">
</div>
```

**After:**
```html
<div class="col-md-3">
  <label class="form-label fw-semibold">
    <i class="fas fa-icon me-1 text-primary"></i>
    Field Name <span class="text-danger">*</span>
  </label>
  <input type="text" class="form-control" placeholder="Enter field name">
  <div class="invalid-feedback">
    Field name is required.
  </div>
</div>
```

### 3. Input Groups with Icons
**Before:**
```html
<div class="input-group">
  <input class="form-control" placeholder="Search">
</div>
```

**After:**
```html
<div class="input-group">
  <span class="input-group-text">
    <i class="fas fa-search text-muted"></i>
  </span>
  <input class="form-control" placeholder="Search">
</div>
```

### 4. Professional Tables
**Before:**
```html
<table class="table table-striped">
  <thead align="center">
    <tr>
      <th>Column</th>
    </tr>
  </thead>
```

**After:**
```html
<div class="table-responsive">
  <table class="table table-striped table-hover">
    <thead class="table-dark">
      <tr class="text-center">
        <th scope="col">
          <i class="fas fa-icon me-1"></i>
          Column
        </th>
      </tr>
    </thead>
```

### 5. Action Buttons
**Before:**
```html
<button class="btn btn-primary">Save</button>
<button class="btn btn-warning">Edit</button>
```

**After:**
```html
<div class="d-flex justify-content-end gap-2">
  <button class="btn btn-success">
    <i class="fas fa-save me-1"></i>
    Save
  </button>
  <button class="btn btn-warning" [disabled]="!selectedItem">
    <i class="fas fa-edit me-1"></i>
    Edit
  </button>
</div>
```

### 6. Select Dropdowns
**Before:**
```html
<select class="form-control">
  <option>-Select-</option>
```

**After:**
```html
<select class="form-select">
  <option value="">-Select Option-</option>
```

### 7. Badges for Status/Categories
**Before:**
```html
<td>{{item.category}}</td>
```

**After:**
```html
<td>
  <span class="badge bg-info">
    {{item.category}}
  </span>
</td>
```

### 8. Empty States
**After:**
```html
<div *ngIf="items.length === 0" class="text-center py-5">
  <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
  <h5 class="text-muted">No items found</h5>
  <p class="text-muted">Try adjusting your search criteria</p>
</div>
```

## Color Scheme Guidelines

### Bootstrap Color Classes
- `text-primary` - Blue (#0d6efd)
- `text-success` - Green (#198754) 
- `text-warning` - Yellow (#ffc107)
- `text-danger` - Red (#dc3545)
- `text-info` - Cyan (#0dcaf0)
- `text-secondary` - Gray (#6c757d)
- `text-dark` - Dark Gray (#212529)
- `text-muted` - Light Gray (#6c757d)

### Icon Color Mapping
- **Create/Add actions**: `text-success`
- **Edit/Update actions**: `text-warning`
- **Delete/Remove actions**: `text-danger`
- **View/List actions**: `text-primary`
- **Info/Details**: `text-info`
- **Settings/Config**: `text-secondary`

## Migration Checklist

### For Each Component:
- [ ] Update card headers with icons and colors
- [ ] Add proper form labels with icons
- [ ] Use `form-select` instead of `form-control` for dropdowns
- [ ] Add input group icons where appropriate
- [ ] Update table headers with icons
- [ ] Add professional button styling with icons
- [ ] Include empty states for lists/tables
- [ ] Use badges for status/category displays
- [ ] Add proper validation feedback
- [ ] Ensure responsive design with proper Bootstrap grid

### Icon Updates:
- [ ] Import `IconService` in component
- [ ] Replace hardcoded icons with service calls
- [ ] Update any remaining `fa` classes to `fas`
- [ ] Add color classes to icons

### Testing:
- [ ] Test responsive behavior on mobile
- [ ] Verify accessibility with screen readers
- [ ] Check color contrast ratios
- [ ] Validate form interactions
- [ ] Test table sorting/filtering if applicable

## Modal Best Practices

### Modal Templates
**Before:**
```html
<ng-template #templateModal>
  <div class="modal-header">
    <h4 class="modal-title float-start">Title</h4>
    <button type="button" class="close float-end" (click)="modalRef.hide()">
      <span>&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-component></app-component>
  </div>
</ng-template>
```

**After:**
```html
<ng-template #templateModal>
  <div class="modal-header bg-primary text-white">
    <h5 class="modal-title">
      <i class="fas fa-icon me-2"></i>
      Modal Title
    </h5>
    <button type="button" class="btn-close btn-close-white"
            aria-label="Close" (click)="modalRef.hide()"></button>
  </div>
  <div class="modal-body p-4">
    <app-component [isModal]="true"></app-component>
  </div>
</ng-template>
```

### Component Modal Support
Add `@Input() isModal: boolean = false;` to components used in modals to conditionally hide headers.

## Common Issues to Fix

1. **Inconsistent spacing**: Use Bootstrap spacing utilities (`mb-3`, `me-2`, etc.)
2. **Mixed icon libraries**: Standardize on FontAwesome 5+
3. **Outdated Bootstrap classes**: Update `form-control` to `form-select` for dropdowns
4. **Missing validation feedback**: Add proper error messages
5. **Poor mobile experience**: Ensure responsive design
6. **Accessibility issues**: Add proper ARIA labels and semantic HTML
7. **Modal close buttons**: Use `btn-close` instead of old `close` class
8. **Button stacking**: Use `d-flex gap-2` for proper button spacing
9. **Modal sizing**: Ensure adequate padding and responsive sizing

## Resources

- [Bootstrap 5 Documentation](https://getbootstrap.com/docs/5.3/)
- [FontAwesome Icons](https://fontawesome.com/icons)
- [Bootstrap Color Utilities](https://getbootstrap.com/docs/5.3/utilities/colors/)
- [Bootstrap Form Validation](https://getbootstrap.com/docs/5.3/forms/validation/)
