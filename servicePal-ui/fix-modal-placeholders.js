const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript files in a directory
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      findTsFiles(filePath, fileList);
    } else if (file.endsWith('.ts') && !file.endsWith('.spec.ts')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix $2 placeholders in modal service calls
function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Find any modalService.show calls with $2 placeholders
  const modalServiceRegex = /this\.modalService\.show\((.*?),\s*{\s*class:\s*\$2\s*}\)/g;

  if (modalServiceRegex.test(content)) {
    // Replace $2 with a proper class string
    content = content.replace(modalServiceRegex, (match, component) => {
      return `this.modalService.show(${component}, { class: 'modal-lg' })`;
    });
    modified = true;
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed: ${filePath}`);
  }
}

// Main function
function main() {
  const srcDir = path.join(__dirname, 'src');
  const tsFiles = findTsFiles(srcDir);

  console.log(`Found ${tsFiles.length} TypeScript files`);

  tsFiles.forEach(file => {
    fixFile(file);
  });

  console.log('Done!');
}

main();
