package lk.sout.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lk.sout.event.CascadeSaveMongoEventListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Collections;


/**
 * Created by Madhawa Weerasinghe on 7/27/2017.
 */
@Configuration
@EnableTransactionManagement
@EnableMongoAuditing
@ComponentScan(basePackages = {"lk.sout.core", "lk.sout.servicePal"})
@EnableMongoRepositories(basePackages = {"lk.sout.core.repository", "lk.sout.servicePal.inventory.repository",
        "lk.sout.servicePal.hr.repository", "lk.sout.servicePal.trade.repository",
        "lk.sout.servicePal.business.repository"})

public class DbConfig extends AbstractMongoClientConfiguration {

    @Bean
    public CascadeSaveMongoEventListener cascadingMongoEventListener() {
        return new CascadeSaveMongoEventListener();
    }

    @Bean
    public AuditorAware<String> auditorProvider() {
        return new SpringSecurityAuditAwareImpl();
    }

    @Bean
    MongoTransactionManager transactionManager(@Qualifier("mongoDbFactory") MongoDatabaseFactory dbFactory) {
        try {
            return new MongoTransactionManager(dbFactory);
        } catch (Exception ex) {
            // Log warning if transaction manager cannot be created (e.g., standalone MongoDB)
            System.out.println("Warning: MongoDB transactions not available. Running in standalone mode.");
            return new MongoTransactionManager(dbFactory);
        }
    }

    @Value("${spring.data.mongodb.host}")
    private String host;

    @Value("${spring.data.mongodb.database}")
    private String dataBase;

    @Value("${spring.data.mongodb.authDatabase}")
    private String authDb;

    @Value("${spring.data.mongodb.port}")
    private int port;

    @Value("${spring.data.mongodb.username}")
    private String username;

    @Value("${spring.data.mongodb.password}")
    private String password;

    @Override
    protected String getDatabaseName() {
        return dataBase;
    }

    @Override
    @Bean
    public MongoClient mongoClient() {
        final ConnectionString connectionString = new ConnectionString("mongodb://" + host + ":" + port + "/" + dataBase);
        final MongoClientSettings mongoClientSettings = MongoClientSettings.builder()
                .applyConnectionString(connectionString).
                        credential(MongoCredential.createCredential(username, authDb, password.toCharArray()))
                .build();
        return MongoClients.create(mongoClientSettings);
    }

}
