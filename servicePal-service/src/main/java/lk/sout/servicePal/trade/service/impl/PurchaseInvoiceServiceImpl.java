/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package lk.sout.servicePal.trade.service.impl;

import lk.sout.servicePal.inventory.service.ItemService;
import lk.sout.servicePal.inventory.service.StockService;
import lk.sout.servicePal.trade.entity.PurchaseInvoice;
import lk.sout.servicePal.trade.repository.PurchaseInvoiceRepository;
import lk.sout.servicePal.trade.service.SupplierService;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.entity.Transaction;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.core.service.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PurchaseInvoiceServiceImpl implements lk.sout.servicePal.trade.service.PurchaseInvoiceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseInvoiceServiceImpl.class);

    @Autowired
    Response response;

    @Autowired
    PurchaseInvoiceRepository purchaseInvoiceRepository;

    @Autowired
    TransactionService transactionService;

    @Autowired
    Transaction transaction;

    @Autowired
    ItemService itemService;

    @Autowired
    StockService stockService;

    @Autowired
    SupplierService supplierService;

    @Autowired
    Environment env;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    Sequence sequence;

    @Override
    @Transactional
    public Response save(PurchaseInvoice purchaseInvoice) {
        try {
            String seqId = "";
            sequence = sequenceService.findSequenceByName("PurchaseInvoice");
            seqId = (sequence.getPrefix() + String.valueOf((sequence.getCounter() + 1)));
            sequence = null;
            purchaseInvoice.setPurchaseInvoiceNo(seqId);

            MetaData piType = metaDataService.searchMetaData("PurchaseInvoice", "Expense");

            if (purchaseInvoice.getTotalAmount() <= purchaseInvoice.getPayment()) {
                purchaseInvoice.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
            } else if (purchaseInvoice.getPayment() == 0) {
                purchaseInvoice.setStatus(metaDataService.searchMetaData("Pending", "PaymentStatus"));
            } else {
                purchaseInvoice.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
            }

            PurchaseInvoice pi = purchaseInvoiceRepository.save(purchaseInvoice);
            sequenceService.incrementSequence("PurchaseInvoice");

            if (!(pi.getPayment() <= 0)) {
                transaction = new Transaction();
                transaction.setAmount(pi.getPayment());
                transaction.setOperator("-");
                transaction.setRefNo(pi.getPurchaseInvoiceNo());
                transaction.setRefType("Purchase Invoice");
                transaction.setType(piType);
                transaction.setThirdParty(pi.getSupplier().getRegNo());
                transaction.setDate(pi.getDate());
                transactionService.save(transaction);
                transaction = new Transaction();
            }

            stockService.createByPurchaseInvRecs(purchaseInvoice.getPurchaseInvoiceRecords(), false);
            response.setCode(200);
            response.setMessage("PI Added Successfully");

            return response;
        } catch (NumberFormatException ex) {
            try {
                // Only set rollback if transaction is active
                if (TransactionAspectSupport.currentTransactionStatus() != null) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
            } catch (Exception txEx) {
                LOGGER.warn("No active transaction to rollback: " + txEx.getMessage());
            }
            LOGGER.error("Adding PI Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Adding PI Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public Iterable<PurchaseInvoice> findAll(Pageable pageable) {
        try {
            return purchaseInvoiceRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<PurchaseInvoice> findAllBySupplierOrderByIdDesc(String supplier) {
        try {
            return purchaseInvoiceRepository.findAllBySupplierOrderByIdDesc(supplier);
        } catch (Exception ex) {
            LOGGER.error("Find All SalesInvoice Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<PurchaseInvoice> findAllByMetaDataNotCompleted(MetaData metaData) {
        try {
            return purchaseInvoiceRepository.findAllByStatusNot(metaData);
        } catch (Exception ex) {
            LOGGER.error("Find All Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public PurchaseInvoice searchByInvoiceNo(String invoiceNo) {
        return purchaseInvoiceRepository.findByInvoiceNo(invoiceNo);
    }

    @Override
    public List<PurchaseInvoice> findBySupplierId(String id) {
        try {
            return purchaseInvoiceRepository.findBySupplier(id);
        } catch (Exception e) {
            LOGGER.error("Find All Customer Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<PurchaseInvoice> findAllByDate(LocalDate date) {
        try {
            return purchaseInvoiceRepository.findAllByDate(date);
        } catch (Exception e) {
            LOGGER.error("Find All PurchaseInvoice Failed " + e.getMessage());
            return null;
        }

    }

    @Override
    public PurchaseInvoice findById(String id) {
        return purchaseInvoiceRepository.findById(id).get();
    }

    @Override
    public PurchaseInvoice findByPurchaseInvoiceNo(String purchaseInvoiceNo) {
        try {
            return purchaseInvoiceRepository.findByPurchaseInvoiceNo(purchaseInvoiceNo);
        } catch (Exception e) {
            LOGGER.error("Find All PurchaseInvoice Failed " + e.getMessage());
            return null;
        }
    }
}
