/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package lk.sout.servicePal.trade.service.impl;

import lk.sout.core.entity.*;
import lk.sout.core.service.*;
import lk.sout.servicePal.business.entity.Job;
import lk.sout.servicePal.business.service.JobService;
import lk.sout.servicePal.inventory.service.ItemService;
import lk.sout.servicePal.inventory.service.StockService;
import lk.sout.servicePal.trade.entity.Customer;
import lk.sout.servicePal.trade.entity.SalesInvoice;
import lk.sout.servicePal.trade.entity.SalesInvoiceRecord;
import lk.sout.servicePal.trade.repository.SalesInvoiceRepository;
import lk.sout.servicePal.trade.service.CashierService;
import lk.sout.servicePal.trade.service.ChequeService;
import lk.sout.servicePal.trade.service.CustomerService;
import lk.sout.servicePal.trade.service.SalesInvoiceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */

@Service
public class SalesInvoiceServiceImpl implements SalesInvoiceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesInvoiceServiceImpl.class);

    @Autowired
    SalesInvoiceRepository salesInvoiceRepository;

    @Autowired
    CustomerService customerService;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    ChequeService chequeService;

    @Autowired
    Response response;

    @Autowired
    TransactionService transactionService;

    @Autowired
    Transaction transaction;

    @Autowired
    CashierService cashierService;

    @Autowired
    UserService userService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    Sequence sequence;

    @Autowired
    JobService jobService;

    @Autowired
    ItemService itemService;

    @Autowired
    StockService stockService;

    @Override
    public Iterable<SalesInvoice> findAll(Pageable pageable) {
        try {
            return salesInvoiceRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Sales invoice Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    @Transactional
    public Response save(SalesInvoice salesInvoice) {

        try {
            User user = userService.findLoggedInUser();

            String seqId = "";
            sequence = sequenceService.findSequenceByName("SalesInvoice");
            seqId = (sequence.getPrefix() + String.valueOf((sequence.getCounter() + 1)));
            salesInvoice.setInvoiceNo(seqId);
            sequence = null;

            salesInvoice.setDate(LocalDateTime.now());

            MetaData cashPayment = metaDataService.searchMetaData("Cash", "PaymentMethod");

            if (salesInvoice.getTotalAmount().compareTo(salesInvoice.getPayment()) <= 0) {
                salesInvoice.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
                salesInvoice.setPaidDate(LocalDateTime.now());
                salesInvoice.setBalance(0.0);
            } else if (salesInvoice.getPayment() == 0) {
                salesInvoice.setStatus(metaDataService.searchMetaData("Pending", "PaymentStatus"));
                salesInvoice.setBalance(salesInvoice.getTotalAmount());
            } else if (salesInvoice.getTotalAmount().compareTo(salesInvoice.getPayment()) > 0 &&
                    salesInvoice.getPayment() > 0) {
                salesInvoice.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
                salesInvoice.setBalance(salesInvoice.getTotalAmount() - salesInvoice.getPayment());
            }

            if (null == salesInvoice.getCustomer()) {
                Customer defaultCustomer = customerService.findDefaultCustomer();
                salesInvoice.setCustomer(defaultCustomer);
                salesInvoice.setCustomerName(defaultCustomer.getName());
            } else {
                if (salesInvoice.getTotalAmount().compareTo(salesInvoice.getPayment()) > 0 ||
                        salesInvoice.getPayment() == 0) {
                    Customer customer = customerService.findById(salesInvoice.getCustomer().getId());
                    Double balance = (null != customer.getBalance() ? customer.getBalance() : 0.0);
                    balance = balance + (salesInvoice.getTotalAmount() - salesInvoice.getPayment());
                    customer.setBalance(balance);
                    customerService.save(customer);
                }
            }

            if (!salesInvoice.isDirectMode()) {
                for (SalesInvoiceRecord record : salesInvoice.getSalesInvoiceRecords()) {
                    record.setItem(itemService.findOneByBarcode(record.getBarcode()));
                }
            }

            if (!salesInvoice.getPaymentMethod().getId().equals(cashPayment.getId())) {
                if (salesInvoice.getTotalAmount() > salesInvoice.getPayment()) {
                    salesInvoice.setCashlessAmount(salesInvoice.getPayment());
                } else {
                    salesInvoice.setCashlessAmount(salesInvoice.getTotalAmount());
                }
            } else {
                salesInvoice.setCashlessAmount(0.00);
            }

            salesInvoice.getSalesInvoiceRecords().forEach(rec -> {
                rec.setDate(LocalDateTime.now());
                rec.setInvoiceNo(salesInvoice.getInvoiceNo());
            });

            SalesInvoice si = salesInvoiceRepository.save(salesInvoice);

            //only for direct sales. Jobs are excluded. cause stock reduced when closing the job
            if (si.isDirectMode()) {
                if (!stockService.deductFromStock(salesInvoice, user.getWarehouseCode()))
                    throw new Exception("Stock not available");
            }

            sequenceService.incrementSequence("SalesInvoice"); //@Changelater

            if (!si.isDirectMode()) {
                Job job = jobService.findByJobNo(salesInvoice.getJobNo());
                MetaData jobClosed = metaDataService.searchMetaData("Job Closed", "Job Status");
                job.setJobStatus(jobClosed);
                jobService.updateJob(job);
            }

            MetaData siType = metaDataService.searchMetaData("SalesInvoice", "Income");
            if (!(si.getPayment() <= 0)) {
                transaction = new Transaction();
                if (si.getTotalAmount() > si.getPayment()) {
                    transaction.setAmount(si.getPayment());
                } else {
                    transaction.setAmount(si.getTotalAmount());
                }
                transaction.setOperator("+");
                transaction.setRefNo(si.getInvoiceNo());
                if (si.isDirectMode()) {
                    transaction.setRefType("Direct Sales Invoice Payment");
                } else {
                    transaction.setRefType("Job Sales Invoice Payment");
                }
                transaction.setType(siType);
                transaction.setDate(si.getDate());
                transactionService.save(transaction);
                transaction.setId(null);
            }

            if (si.getPaymentMethod().getId().equals(cashPayment.getId())) {
                if (si.getTotalAmount() > si.getPayment()) {
                    cashierService.topUpCashier(si.getPayment(), user.getCounter());
                   // cashierService.createLog(si.getPayment(), si.getInvoiceNo(), "Sales Invoice Payment", "+", user.getCounter());
                } else {
                    cashierService.topUpCashier(si.getTotalAmount(), user.getCounter());
                   // cashierService.createLog(si.getTotalAmount(), si.getInvoiceNo(), "Sales Invoice Payment", "+", user.getCounter());
                }
            }

            response.setData(si.getInvoiceNo());
            response.setCode(200);
            response.setMessage("Sales Invoice Created Successfully");
            return response;
        } catch (Exception ex) {
            try {
                // Only set rollback if transaction is active
                if (TransactionAspectSupport.currentTransactionStatus() != null) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
            } catch (Exception txEx) {
                LOGGER.warn("No active transaction to rollback: " + txEx.getMessage());
            }
            LOGGER.error("Creating Sales Invoice Failed on line " + ex.getStackTrace()[0].getLineNumber() + " : " +
                    ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Sales Invoice Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }


    //only direct sales invoices are allowed to amend
    @Override
    @Transactional
    public Response amendSi(String invoiceNo) {

        try {
            User user = userService.findLoggedInUser();

            SalesInvoice salesInvoice = salesInvoiceRepository.findByInvoiceNo(invoiceNo);

            if (null != salesInvoice.getJobNo()) {
                throw new Exception("Not a Direct sales invoice");
            }

            Double payment = salesInvoice.getPayment();
            salesInvoice.setStatus(metaDataService.searchMetaData("Cancelled", "PaymentStatus"));
            salesInvoice.setPayment(0.0);
            salesInvoice.setBalance(0.0);
            salesInvoice.setCashBalance(0.0);
            //kept this to monitor
            //salesInvoice.setSubTotal(0.0);
            salesInvoice.setTotalAmount(0.0);
            salesInvoice.setTotalDiscount(0.0);
            salesInvoice.setCashlessAmount(0.0);

            for (SalesInvoiceRecord salesInvoiceRecord : salesInvoice.getSalesInvoiceRecords()) {
                Double qty = salesInvoiceRecord.getQuantity();
                stockService.topUpStock(salesInvoiceRecord.getItemCode(), user.getWarehouseCode(), qty, true);
                salesInvoiceRecord.setQuantity(0.0);
                salesInvoiceRecord.setDiscount(0.0);
                salesInvoiceRecord.setPrice(0.0);
                salesInvoiceRecord.setSubTotal(0.0);
                salesInvoiceRecord.setUnitPrice(0.0);
            }
            salesInvoiceRepository.save(salesInvoice);

            cashierService.deductFromCashier(payment, user.getCounter());

            Transaction transaction = transactionService.findByRefNoAndRefType(salesInvoice.getInvoiceNo(),
                    "Direct Sales Invoice Payment");
            transaction.setAmount(0.0);
            transaction.setParam1("Transaction Canceled");
            transactionService.save(transaction);

            response.setData(invoiceNo);
            response.setCode(200);
            response.setMessage("Sales Invoice Amended Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Amending Sales Invoice Failed " + ex.getStackTrace()[0].getLineNumber() + " : " +
                    ex.getMessage());
            response.setCode(501);
            response.setMessage("Amending Sales Invoice Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public Iterable<SalesInvoice> findAllPendingPages(Pageable pageable) {
        try {
            MetaData pendingSi = metaDataService.searchMetaData("Pending", "PaymentStatus");
            return salesInvoiceRepository.findAllByStatus(pendingSi, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Sales invoice by Status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllPendingSiForMonth() {
        try {
            MetaData pendingSi = metaDataService.searchMetaData("Pending", "PaymentStatus");
            return salesInvoiceRepository.findAllByStatusAndCreatedDateBetween(pendingSi,
                    LocalDateTime.now().withDayOfMonth(1), LocalDateTime.now());
        } catch (Exception ex) {
            LOGGER.error("Find All Sales invoice by Status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<SalesInvoice> findAllPendingByRange(String rangeId, Pageable pageable) {
        try {
            MetaData pendingSi = metaDataService.searchMetaData("Pending", "PaymentStatus");
            MetaData range = metaDataService.findById(rangeId);
            LocalDate sDate = LocalDate.now();
            LocalDate eDate = LocalDate.now();
            LocalDate today = LocalDate.now();

            if (range.getValue().equals("Today")) {
                sDate = today;
                eDate = today.plusDays(1);
            }
            if (range.getValue().equals("This Week")) {
                sDate = today.with((DayOfWeek.MONDAY));
                eDate = today.with((DayOfWeek.SUNDAY)).plusDays(1);
            }
            if (range.getValue().equals("This Month")) {
                sDate = today.withDayOfMonth(1);
                eDate = today.withDayOfMonth(today.lengthOfMonth());
            }
            if (range.getValue().equals("This Year")) {
                sDate = today.withDayOfYear(1);
                eDate = today.withDayOfYear(today.lengthOfYear());
            }
            return salesInvoiceRepository.findAllByStatusAndCreatedDateBetween(pendingSi, sDate.atStartOfDay(),
                    eDate.atStartOfDay(), pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Sales invoice by Status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllPendingBetween(LocalDate sDate, LocalDate eDate) {
        try {
            MetaData pendingSi = metaDataService.searchMetaData("Paid", "PaymentStatus");
            return salesInvoiceRepository.findAllByStatusNotAndCreatedDateBetween(pendingSi, sDate.atStartOfDay(),
                    eDate.atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Find All Sales invoice by Status And Between Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllByOrderByIdDesc() {
        try {
            return salesInvoiceRepository.findAllByOrderByIdDesc();
        } catch (Exception ex) {
            LOGGER.error("Find All SalesInvoice Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllByCustomerOrderByIdDesc(String customer) {
        try {
            return salesInvoiceRepository.findAllByCustomerOrderByIdDesc(customer);
        } catch (Exception ex) {
            LOGGER.error("Find All SalesInvoice Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findByCustomerNicBr(String nicBr) {
        try {
            Customer customer = customerService.findByNicBr(nicBr);
            return salesInvoiceRepository.findAllByCustomer(customer);
        } catch (Exception ex) {
            LOGGER.error("Find by customer failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllByCustomerPending(String nicBr) {
        try {
            Customer customer = customerService.findByNicBr(nicBr);
            MetaData pendingSi = metaDataService.searchMetaData("Paid", "PaymentStatus");
            return salesInvoiceRepository.findAllByCustomerAndStatusNot(customer, pendingSi);
        } catch (Exception ex) {
            LOGGER.error("Find by customer failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public SalesInvoice findByJobNo(String jobNo) {
        try {
            return salesInvoiceRepository.findByJobNo(jobNo);
        } catch (Exception ex) {
            LOGGER.error("Find by customer failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public SalesInvoice findByInvNo(String invNo) {
        try {
            return salesInvoiceRepository.findByInvoiceNo(invNo);
        } catch (Exception ex) {
            LOGGER.error("Find by customer failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllByPaymentMethod(String paymentMethod) {
        return salesInvoiceRepository.findAllByPaymentMethodValue(paymentMethod);
    }

    @Override
    public List<SalesInvoice> findByDate(LocalDate Date) {
        try {
            return salesInvoiceRepository.findByDate(Date);
        } catch (Exception e) {
            LOGGER.error("Find All SalesInvoice Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllByMetaDataNotCompleted(MetaData metaData) {
        try {
            return salesInvoiceRepository.findAllByStatusNot(metaData);
        } catch (Exception ex) {
            LOGGER.error("Find All Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findBySalesInvoiceIdLikeIgnoreCase(String invoiceNo) {
        try {
            return salesInvoiceRepository.findByInvoiceNoLikeIgnoreCase(invoiceNo);
        } catch (Exception e) {
            LOGGER.error("Find All SalesInvoice Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    @Transactional
    public Response payBalance(String siNo, Double amount) {
        try {
            SalesInvoice si = salesInvoiceRepository.findByInvoiceNo(siNo);

            if (si.getBalance().compareTo(amount) <= 0) {
                si.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
                si.setPaidDate(LocalDateTime.now());
            } else if (si.getBalance().compareTo(amount) > 0 && amount > 0) {
                si.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
            }
            si.setPayment(si.getPayment() + amount);
            Double balance = si.getBalance() - amount;
            si.setBalance(balance.compareTo(0.0) < 0.0 ? 0.0 : balance);
            salesInvoiceRepository.save(si);

            MetaData siType = metaDataService.searchMetaData("SalesInvoice", "Income");

            if (amount.compareTo(0.0) > 0) {
                transaction = new Transaction();
                transaction.setAmount(amount);
                transaction.setOperator("+");
                transaction.setRefNo(si.getInvoiceNo());
                transaction.setRefType("Past Sales Invoice Payment");
                transaction.setType(siType);
                transaction.setDate(LocalDateTime.now());
                transactionService.save(transaction);
            }
            response.setCode(200);
            response.setMessage("Sales Invoice Paid Successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Pay Balance Failed " + ex.getMessage());
            try {
                // Only set rollback if transaction is active
                if (TransactionAspectSupport.currentTransactionStatus() != null) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
            } catch (Exception txEx) {
                LOGGER.warn("No active transaction to rollback: " + txEx.getMessage());
            }
            response.setData(ex.getMessage());
            response.setCode(500);
            response.setMessage("Sales Invoice Payment Failed");
            return response;
        }
    }

    @Override
    public SalesInvoice findById(String id) {
        return salesInvoiceRepository.findById(id).get();
    }
}
