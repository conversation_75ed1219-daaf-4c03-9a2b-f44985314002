spring.application.name=ServicePal

#sout.customer=
sout.customer=Amb
#sout.customer=Tissa
#sout.customer=Maharagama

#mongodb
spring.data.mongodb.host=localhost
#spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.database=servicePal${sout.customer}
spring.data.mongodb.authDatabase=servicePal${sout.customer}
spring.data.mongodb.username=servicePal
spring.data.mongodb.password=awer@#$cdfDDF!@S_+(

spring.data.mongodb.auto-index-creation=true
spring.main.allow-circular-references=true

#logging
#logging.level.root=info
#logging.pattern.console=%d{dd-MM-yyyy HH:mm:ss.SSS} %magenta([%thread]) %highlight(%-5level) %logger.%M - %msg%n
#logging.file.path=/var/log/tomcat/sout.servicePalTissa.log
#logging.pattern.file=%d{dd-MM-yyyy HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%M - %msg%n
#logging.level.org.springframework.data=DEBUG
#logging.level.org.apache.http=DEBUG
#logging.level.org.apache.http.wire=DEBUG
#org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
location.company.logo=../webapps/uploads/image/company/

# MULTIPART (MultipartProperties)
# Enable multipart uploads
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=215MB

dateFormat=MM/dd/yyyy
dateTimeFormat=MM/dd/yyyy HH:mm:ss

#spring.boot.admin.client.username=madhawa
#spring.boot.admin.client.password=madhawa
#spring.boot.admin.client.enabled=true
#spring.boot.admin.client.auto-registration=true
#spring.boot.admin.client.instance.service-base-url=http://*************
#spring.boot.admin.client.url=http://*************:8080
#management.endpoints.web.exposure.include=*
#management.endpoint.health.show-details=always

baseDir=/opt/tomcat/
uploadPath=uploads/${sout.customer}/images/
